package com.Automation.Scripts;

import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.openqa.selenium.support.ui.ExpectedConditions;
import java.time.Duration;

/**
 * Simple Training Venue Registration Script
 * Based on accurate screenshot analysis
 */
public class SimpleTrainingVenueScript {
    
    public static void main(String[] args) {
        WebDriver driver = new ChromeDriver();
        WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(10));
        
        try {
            // Navigate to the application
            driver.get("http://cqmsconfig05/EPIQ/Pages/MainPage?TaskId=#");
            driver.manage().window().maximize();
            
            // Wait for page to load
            Thread.sleep(3000);
            
            System.out.println("Starting Training Venue Registration...");

            // Try multiple strategies to fill the form
            boolean success = fillFormByPosition(driver, wait);

            if (!success) {
                System.out.println("Position-based filling failed, trying structure-based approach...");
                fillFormByStructure(driver);
            }
            
            // Click Submit button - try multiple locators
            clickSubmitButton(driver, wait);
            
            System.out.println("Form submitted successfully!");
            
            // Wait to see the result
            Thread.sleep(5000);
            
        } catch (Exception e) {
            System.out.println("Error during form filling: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // Uncomment the line below to close browser
            // driver.quit();
        }
    }
    
    /**
     * Fill form using position-based locators
     */
    private static boolean fillFormByPosition(WebDriver driver, WebDriverWait wait) {
        try {
            String[] values = {
                "Conference Hall A",      // Training Venue Name
                "Room-101",              // Room Number
                "50",                    // Capacity
                "9876543210",            // Phone Number
                "123 Business Park",     // Address1
                "New York",              // City
                "10001",                 // Pin/Zip
                "100",                   // Cost Per Hour
                "VENUE001",              // Unique Code
                "Conference Room",       // Room Type
                "John Doe",              // Contact Person
                "<EMAIL>",  // Email ID
                "Building A, Floor 2",   // Address2
                "NY"                     // State
            };

            // Try different input locator strategies
            String[] inputLocators = {
                "//input[@type='text']",
                "//input[not(@type) or @type='text']",
                "//input[@class='form-control']",
                "//div[@class='form-group']//input",
                "//form//input[@type='text']",
                "//table//input[@type='text']"
            };

            java.util.List<WebElement> inputFields = null;

            // Try each locator strategy until one works
            for (String locator : inputLocators) {
                try {
                    inputFields = driver.findElements(By.xpath(locator));
                    if (inputFields.size() >= values.length) {
                        System.out.println("Found " + inputFields.size() + " input fields using: " + locator);
                        break;
                    }
                } catch (Exception e) {
                    continue;
                }
            }

            if (inputFields == null || inputFields.size() < values.length) {
                System.out.println("Could not find enough input fields");
                return false;
            }

            // Fill input fields
            for (int i = 0; i < Math.min(inputFields.size(), values.length); i++) {
                try {
                    WebElement field = inputFields.get(i);
                    if (field.isDisplayed() && field.isEnabled()) {
                        field.clear();
                        field.sendKeys(values[i]);
                        System.out.println("Filled field " + (i+1) + ": " + values[i]);
                        Thread.sleep(300);
                    }
                } catch (Exception e) {
                    System.out.println("Could not fill field " + (i+1) + ": " + e.getMessage());
                }
            }

            // Fill textarea fields
            fillTextAreas(driver);

            return true;

        } catch (Exception e) {
            System.out.println("Error in position-based form filling: " + e.getMessage());
            return false;
        }
    }

    /**
     * Fill textarea fields
     */
    private static void fillTextAreas(WebDriver driver) {
        try {
            java.util.List<WebElement> textAreas = driver.findElements(By.xpath("//textarea"));
            String[] textAreaValues = {
                "Projector, Whiteboard, Audio System",
                "Air conditioned room with parking facility"
            };

            for (int i = 0; i < Math.min(textAreas.size(), textAreaValues.length); i++) {
                try {
                    WebElement field = textAreas.get(i);
                    if (field.isDisplayed() && field.isEnabled()) {
                        field.clear();
                        field.sendKeys(textAreaValues[i]);
                        System.out.println("Filled textarea " + (i+1) + ": " + textAreaValues[i]);
                        Thread.sleep(300);
                    }
                } catch (Exception e) {
                    System.out.println("Could not fill textarea " + (i+1) + ": " + e.getMessage());
                }
            }
        } catch (Exception e) {
            System.out.println("Error filling textareas: " + e.getMessage());
        }
    }

    /**
     * Click submit button using multiple strategies
     */
    private static void clickSubmitButton(WebDriver driver, WebDriverWait wait) {
        String[] submitLocators = {
            "//button[text()='Submit']",
            "//input[@type='submit']",
            "//button[@id='btnSubmit']",
            "//input[@value='Submit']",
            "//button[contains(text(),'Submit')]",
            "//button[contains(@class,'submit')]",
            "//input[contains(@class,'submit')]"
        };

        for (String locator : submitLocators) {
            try {
                WebElement submitButton = wait.until(ExpectedConditions.elementToBeClickable(By.xpath(locator)));
                submitButton.click();
                System.out.println("Submit button clicked using: " + locator);
                return;
            } catch (Exception e) {
                continue;
            }
        }

        System.out.println("Could not find submit button with any locator strategy");
    }
    
    /**
     * Alternative method using DOM inspection and multiple strategies
     */
    public static void fillFormByStructure(WebDriver driver) {
        try {
            System.out.println("Trying DOM inspection approach...");

            // Strategy 1: Look for form elements
            java.util.List<WebElement> forms = driver.findElements(By.xpath("//form"));
            if (!forms.isEmpty()) {
                System.out.println("Found " + forms.size() + " form(s)");
                fillFormElements(forms.get(0));
                return;
            }

            // Strategy 2: Look for table-based forms
            java.util.List<WebElement> tables = driver.findElements(By.xpath("//table"));
            if (!tables.isEmpty()) {
                System.out.println("Found " + tables.size() + " table(s), trying table-based form");
                fillTableForm(driver);
                return;
            }

            // Strategy 3: Look for div-based forms
            java.util.List<WebElement> divs = driver.findElements(By.xpath("//div[contains(@class,'form') or contains(@class,'container')]"));
            if (!divs.isEmpty()) {
                System.out.println("Found " + divs.size() + " form div(s)");
                fillDivForm(driver);
                return;
            }

            // Strategy 4: Brute force - find all inputs
            fillAllInputs(driver);

        } catch (Exception e) {
            System.out.println("Error in structure-based form filling: " + e.getMessage());
        }
    }

    private static void fillFormElements(WebElement form) {
        try {
            java.util.List<WebElement> inputs = form.findElements(By.xpath(".//input[@type='text' or not(@type)]"));
            java.util.List<WebElement> textareas = form.findElements(By.xpath(".//textarea"));

            String[] values = getTestValues();

            for (int i = 0; i < Math.min(inputs.size(), values.length); i++) {
                fillSingleField(inputs.get(i), values[i], "Form input " + (i+1));
            }

            fillTextAreasWithValues(textareas);

        } catch (Exception e) {
            System.out.println("Error filling form elements: " + e.getMessage());
        }
    }

    private static void fillTableForm(WebDriver driver) {
        try {
            java.util.List<WebElement> inputs = driver.findElements(By.xpath("//table//input[@type='text' or not(@type)]"));
            java.util.List<WebElement> textareas = driver.findElements(By.xpath("//table//textarea"));

            String[] values = getTestValues();

            for (int i = 0; i < Math.min(inputs.size(), values.length); i++) {
                fillSingleField(inputs.get(i), values[i], "Table input " + (i+1));
            }

            fillTextAreasWithValues(textareas);

        } catch (Exception e) {
            System.out.println("Error filling table form: " + e.getMessage());
        }
    }

    private static void fillDivForm(WebDriver driver) {
        try {
            java.util.List<WebElement> inputs = driver.findElements(By.xpath("//div//input[@type='text' or not(@type)]"));
            java.util.List<WebElement> textareas = driver.findElements(By.xpath("//div//textarea"));

            String[] values = getTestValues();

            for (int i = 0; i < Math.min(inputs.size(), values.length); i++) {
                fillSingleField(inputs.get(i), values[i], "Div input " + (i+1));
            }

            fillTextAreasWithValues(textareas);

        } catch (Exception e) {
            System.out.println("Error filling div form: " + e.getMessage());
        }
    }

    private static void fillAllInputs(WebDriver driver) {
        try {
            System.out.println("Trying brute force approach - finding all inputs...");

            java.util.List<WebElement> allInputs = driver.findElements(By.xpath("//input"));
            java.util.List<WebElement> textInputs = new java.util.ArrayList<>();

            // Filter for text inputs
            for (WebElement input : allInputs) {
                try {
                    String type = input.getAttribute("type");
                    if (type == null || type.equals("text") || type.equals("")) {
                        if (input.isDisplayed() && input.isEnabled()) {
                            textInputs.add(input);
                        }
                    }
                } catch (Exception e) {
                    continue;
                }
            }

            System.out.println("Found " + textInputs.size() + " text inputs");

            String[] values = getTestValues();

            for (int i = 0; i < Math.min(textInputs.size(), values.length); i++) {
                fillSingleField(textInputs.get(i), values[i], "Input " + (i+1));
            }

            // Fill textareas
            java.util.List<WebElement> textareas = driver.findElements(By.xpath("//textarea"));
            fillTextAreasWithValues(textareas);

        } catch (Exception e) {
            System.out.println("Error in brute force filling: " + e.getMessage());
        }
    }

    private static void fillSingleField(WebElement field, String value, String fieldName) {
        try {
            field.clear();
            field.sendKeys(value);
            System.out.println("Filled " + fieldName + ": " + value);
            Thread.sleep(200);
        } catch (Exception e) {
            System.out.println("Could not fill " + fieldName + ": " + e.getMessage());
        }
    }

    private static void fillTextAreasWithValues(java.util.List<WebElement> textareas) {
        String[] textAreaValues = {
            "Projector, Whiteboard, Audio System",
            "Air conditioned room with parking facility"
        };

        for (int i = 0; i < Math.min(textareas.size(), textAreaValues.length); i++) {
            fillSingleField(textareas.get(i), textAreaValues[i], "Textarea " + (i+1));
        }
    }

    private static String[] getTestValues() {
        return new String[] {
            "Conference Hall A",      // Training Venue Name
            "Room-101",              // Room Number
            "50",                    // Capacity
            "9876543210",            // Phone Number
            "123 Business Park",     // Address1
            "New York",              // City
            "10001",                 // Pin/Zip
            "100",                   // Cost Per Hour
            "VENUE001",              // Unique Code
            "Conference Room",       // Room Type
            "John Doe",              // Contact Person
            "<EMAIL>",  // Email ID
            "Building A, Floor 2",   // Address2
            "NY"                     // State
        };
    }
}
