package com.Automation.Scripts;

import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.openqa.selenium.support.ui.ExpectedConditions;
import java.time.Duration;

/**
 * Simple Training Venue Registration Script
 * Based on accurate screenshot analysis
 */
public class SimpleTrainingVenueScript {
    
    public static void main(String[] args) {
        WebDriver driver = new ChromeDriver();
        WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(10));
        
        try {
            // Navigate to the application
            driver.get("http://cqmsconfig05/EPIQ/Pages/MainPage?TaskId=#");
            driver.manage().window().maximize();
            
            // Wait for page to load
            Thread.sleep(3000);
            
            System.out.println("Starting Training Venue Registration...");
            
            // Fill Left Column Fields - Using exact placeholder text from screenshot
            fillFieldByPlaceholder(driver, wait, "Training Venue Name*", "Conference Hall A");
            fillFieldByPlaceholder(driver, wait, "Room Number / Name*", "Room-101");
            fillFieldByPlaceholder(driver, wait, "Capacity*", "50");
            fillFieldByPlaceholder(driver, wait, "Phone Number*", "9876543210");
            fillFieldByPlaceholder(driver, wait, "Address1", "123 Business Park");
            fillFieldByPlaceholder(driver, wait, "City", "New York");
            fillFieldByPlaceholder(driver, wait, "Pin / Zip", "10001");
            fillFieldByPlaceholder(driver, wait, "Cost Per Hour", "100");
            
            // Fill Right Column Fields - Using exact placeholder text from screenshot
            fillFieldByPlaceholder(driver, wait, "Unique Code*", "VENUE001");
            fillFieldByPlaceholder(driver, wait, "Room Type*", "Conference Room");
            fillFieldByPlaceholder(driver, wait, "Contact Person*", "John Doe");
            fillFieldByPlaceholder(driver, wait, "Email ID", "<EMAIL>");
            fillFieldByPlaceholder(driver, wait, "Address2", "Building A, Floor 2");
            fillFieldByPlaceholder(driver, wait, "State", "NY");
            
            // Fill textarea fields
            fillTextAreaByPlaceholder(driver, wait, "Room Equipment", "Projector, Whiteboard, Audio System");
            fillTextAreaByPlaceholder(driver, wait, "Additional Information", "Air conditioned room with parking facility");
            
            // Click Submit button
            WebElement submitButton = wait.until(ExpectedConditions.elementToBeClickable(
                By.xpath("//button[text()='Submit']")));
            submitButton.click();
            
            System.out.println("Form submitted successfully!");
            
            // Wait to see the result
            Thread.sleep(5000);
            
        } catch (Exception e) {
            System.out.println("Error during form filling: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // Uncomment the line below to close browser
            // driver.quit();
        }
    }
    
    /**
     * Fill input field by placeholder text
     */
    private static void fillFieldByPlaceholder(WebDriver driver, WebDriverWait wait, String placeholder, String value) {
        try {
            // Primary locator - exact placeholder match
            WebElement field = wait.until(ExpectedConditions.elementToBeClickable(
                By.xpath("//input[@placeholder='" + placeholder + "']")));
            field.clear();
            field.sendKeys(value);
            System.out.println("Filled '" + placeholder + "': " + value);
            Thread.sleep(500); // Small wait between fields
        } catch (Exception e) {
            System.out.println("Could not fill field '" + placeholder + "': " + e.getMessage());
            
            // Try alternative locator strategies
            try {
                // Try partial placeholder match
                WebElement field = driver.findElement(
                    By.xpath("//input[contains(@placeholder, '" + placeholder.replace("*", "") + "')]"));
                field.clear();
                field.sendKeys(value);
                System.out.println("Filled '" + placeholder + "' (alternative): " + value);
            } catch (Exception e2) {
                System.out.println("Failed to fill '" + placeholder + "' with alternative locator: " + e2.getMessage());
            }
        }
    }
    
    /**
     * Fill textarea field by placeholder text
     */
    private static void fillTextAreaByPlaceholder(WebDriver driver, WebDriverWait wait, String placeholder, String value) {
        try {
            WebElement field = wait.until(ExpectedConditions.elementToBeClickable(
                By.xpath("//textarea[@placeholder='" + placeholder + "']")));
            field.clear();
            field.sendKeys(value);
            System.out.println("Filled '" + placeholder + "': " + value);
            Thread.sleep(500); // Small wait between fields
        } catch (Exception e) {
            System.out.println("Could not fill textarea '" + placeholder + "': " + e.getMessage());
        }
    }
    
    /**
     * Alternative method using form structure if placeholders don't work
     */
    public static void fillFormByStructure(WebDriver driver) {
        try {
            System.out.println("Trying alternative form filling by structure...");
            
            // Get all input fields in order
            java.util.List<WebElement> inputFields = driver.findElements(By.xpath("//input[@type='text' or not(@type)]"));
            java.util.List<WebElement> textAreas = driver.findElements(By.xpath("//textarea"));
            
            String[] values = {
                "Conference Hall A",      // Training Venue Name
                "Room-101",              // Room Number
                "50",                    // Capacity  
                "9876543210",            // Phone Number
                "123 Business Park",     // Address1
                "New York",              // City
                "10001",                 // Pin/Zip
                "100",                   // Cost Per Hour
                "VENUE001",              // Unique Code
                "Conference Room",       // Room Type
                "John Doe",              // Contact Person
                "<EMAIL>",  // Email ID
                "Building A, Floor 2",   // Address2
                "NY"                     // State
            };
            
            // Fill input fields
            for (int i = 0; i < Math.min(inputFields.size(), values.length); i++) {
                try {
                    inputFields.get(i).clear();
                    inputFields.get(i).sendKeys(values[i]);
                    System.out.println("Filled field " + (i+1) + ": " + values[i]);
                    Thread.sleep(300);
                } catch (Exception e) {
                    System.out.println("Could not fill field " + (i+1) + ": " + e.getMessage());
                }
            }
            
            // Fill textarea fields
            String[] textAreaValues = {
                "Projector, Whiteboard, Audio System",
                "Air conditioned room with parking facility"
            };
            
            for (int i = 0; i < Math.min(textAreas.size(), textAreaValues.length); i++) {
                try {
                    textAreas.get(i).clear();
                    textAreas.get(i).sendKeys(textAreaValues[i]);
                    System.out.println("Filled textarea " + (i+1) + ": " + textAreaValues[i]);
                    Thread.sleep(300);
                } catch (Exception e) {
                    System.out.println("Could not fill textarea " + (i+1) + ": " + e.getMessage());
                }
            }
            
        } catch (Exception e) {
            System.out.println("Error in alternative form filling: " + e.getMessage());
        }
    }
}
