package com.Automation.learniqObjects;

import java.util.HashMap;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import com.Automation.learniqBase.OQActionEngine;
import com.Automation.Strings.CommonStrings;
import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.TimeUtil;

public class CM_TrainingVenueRegistration extends OQActionEngine {

    // Left Column Fields - Using name/id locators
    @FindBy(name = "VenueName")
    WebElement trainingVenueNameField;

    @FindBy(name = "RoomNumber")
    WebElement roomNumberField;

    @FindBy(name = "Capacity")
    WebElement capacityField;

    @FindBy(name = "PhoneNumber")
    WebElement phoneNumberField;

    @FindBy(name = "Address1")
    WebElement address1Field;

    @FindBy(name = "City")
    WebElement cityField;

    @FindBy(name = "PinZip")
    WebElement pinZipField;

    @FindBy(name = "CostPerHour")
    WebElement costPerHourField;

    // Right Column Fields
    @FindBy(name = "UniqueCode")
    WebElement uniqueCodeField;

    @FindBy(name = "RoomType")
    WebElement roomTypeField;

    @FindBy(name = "ContactPerson")
    WebElement contactPersonField;

    @FindBy(name = "EmailID")
    WebElement emailIdField;

    @FindBy(name = "Address2")
    WebElement address2Field;

    @FindBy(name = "State")
    WebElement stateField;

    @FindBy(name = "RoomEquipment")
    WebElement roomEquipmentField;

    @FindBy(name = "AdditionalInfo")
    WebElement additionalInfoField;

    // Buttons
    @FindBy(xpath = "//button[text()='Submit']")
    WebElement submitButton;

    @FindBy(xpath = "//button[text()='View Existing']")
    WebElement viewExistingButton;
    
    // Constructor
    public CM_TrainingVenueRegistration() {
        PageFactory.initElements(driver, this);
    }

    // Complete form filling and submission method
    public void submitTrainingVenueForm(HashMap<String, String> testData) {
        // Fill mandatory fields
        sendKeys2(trainingVenueNameField, "Enter Training Venue Name", testData.get("trainingVenueName"),
                CommonStrings.sendKeys_AC.getCommonStrings(),
                CommonStrings.sendKeys_AR.getCommonStrings(), "Training Venue Name field");
        TimeUtil.shortWait();

        sendKeys2(roomNumberField, "Enter Room Number", testData.get("roomNumber"),
                CommonStrings.sendKeys_AC.getCommonStrings(),
                CommonStrings.sendKeys_AR.getCommonStrings(), "Room Number field");
        TimeUtil.shortWait();

        sendKeys2(capacityField, "Enter Capacity", testData.get("capacity"),
                CommonStrings.sendKeys_AC.getCommonStrings(),
                CommonStrings.sendKeys_AR.getCommonStrings(), "Capacity field");
        TimeUtil.shortWait();

        sendKeys2(phoneNumberField, "Enter Phone Number", testData.get("phoneNumber"),
                CommonStrings.sendKeys_AC.getCommonStrings(),
                CommonStrings.sendKeys_AR.getCommonStrings(), "Phone Number field");
        TimeUtil.shortWait();

        sendKeys2(uniqueCodeField, "Enter Unique Code", testData.get("uniqueCode"),
                CommonStrings.sendKeys_AC.getCommonStrings(),
                CommonStrings.sendKeys_AR.getCommonStrings(), "Unique Code field");
        TimeUtil.shortWait();

        sendKeys2(roomTypeField, "Enter Room Type", testData.get("roomType"),
                CommonStrings.sendKeys_AC.getCommonStrings(),
                CommonStrings.sendKeys_AR.getCommonStrings(), "Room Type field");
        TimeUtil.shortWait();

        sendKeys2(contactPersonField, "Enter Contact Person", testData.get("contactPerson"),
                CommonStrings.sendKeys_AC.getCommonStrings(),
                CommonStrings.sendKeys_AR.getCommonStrings(), "Contact Person field");
        TimeUtil.shortWait();

        // Fill optional fields if provided
        if (testData.get("address1") != null && !testData.get("address1").isEmpty()) {
            sendKeys2(address1Field, "Enter Address1", testData.get("address1"),
                    CommonStrings.sendKeys_AC.getCommonStrings(),
                    CommonStrings.sendKeys_AR.getCommonStrings(), "Address1 field");
            TimeUtil.shortWait();
        }

        if (testData.get("city") != null && !testData.get("city").isEmpty()) {
            sendKeys2(cityField, "Enter City", testData.get("city"),
                    CommonStrings.sendKeys_AC.getCommonStrings(),
                    CommonStrings.sendKeys_AR.getCommonStrings(), "City field");
            TimeUtil.shortWait();
        }

        if (testData.get("pinZip") != null && !testData.get("pinZip").isEmpty()) {
            sendKeys2(pinZipField, "Enter Pin/Zip", testData.get("pinZip"),
                    CommonStrings.sendKeys_AC.getCommonStrings(),
                    CommonStrings.sendKeys_AR.getCommonStrings(), "Pin/Zip field");
            TimeUtil.shortWait();
        }

        if (testData.get("costPerHour") != null && !testData.get("costPerHour").isEmpty()) {
            sendKeys2(costPerHourField, "Enter Cost Per Hour", testData.get("costPerHour"),
                    CommonStrings.sendKeys_AC.getCommonStrings(),
                    CommonStrings.sendKeys_AR.getCommonStrings(), "Cost Per Hour field");
            TimeUtil.shortWait();
        }

        if (testData.get("emailId") != null && !testData.get("emailId").isEmpty()) {
            sendKeys2(emailIdField, "Enter Email ID", testData.get("emailId"),
                    CommonStrings.sendKeys_AC.getCommonStrings(),
                    CommonStrings.sendKeys_AR.getCommonStrings(), "Email ID field");
            TimeUtil.shortWait();
        }

        if (testData.get("address2") != null && !testData.get("address2").isEmpty()) {
            sendKeys2(address2Field, "Enter Address2", testData.get("address2"),
                    CommonStrings.sendKeys_AC.getCommonStrings(),
                    CommonStrings.sendKeys_AR.getCommonStrings(), "Address2 field");
            TimeUtil.shortWait();
        }

        if (testData.get("state") != null && !testData.get("state").isEmpty()) {
            sendKeys2(stateField, "Enter State", testData.get("state"),
                    CommonStrings.sendKeys_AC.getCommonStrings(),
                    CommonStrings.sendKeys_AR.getCommonStrings(), "State field");
            TimeUtil.shortWait();
        }

        if (testData.get("roomEquipment") != null && !testData.get("roomEquipment").isEmpty()) {
            sendKeys2(roomEquipmentField, "Enter Room Equipment", testData.get("roomEquipment"),
                    CommonStrings.sendKeys_AC.getCommonStrings(),
                    CommonStrings.sendKeys_AR.getCommonStrings(), "Room Equipment field");
            TimeUtil.shortWait();
        }

        if (testData.get("additionalInfo") != null && !testData.get("additionalInfo").isEmpty()) {
            sendKeys2(additionalInfoField, "Enter Additional Information", testData.get("additionalInfo"),
                    CommonStrings.sendKeys_AC.getCommonStrings(),
                    CommonStrings.sendKeys_AR.getCommonStrings(), "Additional Information field");
            TimeUtil.shortWait();
        }

        // Submit the form
        TimeUtil.mediumWait();
        click2(submitButton, CommonStrings.Submit_Button_DC.getCommonStrings(),
                CommonStrings.Submit_Button_AC.getCommonStrings(),
                CommonStrings.Submit_Button_AR.getCommonStrings(),
                CommonStrings.Submit_Button_SS.getCommonStrings());
    }
}
