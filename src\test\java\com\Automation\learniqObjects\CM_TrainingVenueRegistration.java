package com.Automation.learniqObjects;

import java.util.HashMap;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import com.Automation.learniqBase.OQActionEngine;
import com.Automation.Strings.CommonStrings;
import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.TimeUtil;

public class CM_TrainingVenueRegistration extends OQActionEngine {

    // Training Venue Fields - Using EPIQ application ID/Name patterns
    @FindBy(id = "TrainingVenue_VenueName")
    WebElement trainingVenueNameField;

    @FindBy(id = "TrainingVenue_RoomNumber")
    WebElement roomNumberField;

    @FindBy(id = "TrainingVenue_Capacity")
    WebElement capacityField;

    @FindBy(id = "TrainingVenue_PhoneNumber")
    WebElement phoneNumberField;

    @FindBy(id = "TrainingVenue_Address1")
    WebElement address1Field;

    @FindBy(id = "TrainingVenue_City")
    WebElement cityField;

    @FindBy(id = "TrainingVenue_PinZip")
    WebElement pinZipField;

    @FindBy(id = "TrainingVenue_CostPerHour")
    WebElement costPerHourField;

    @FindBy(id = "TrainingVenue_UniqueCode")
    WebElement uniqueCodeField;

    @FindBy(id = "TrainingVenue_RoomType")
    WebElement roomTypeField;

    @FindBy(id = "TrainingVenue_ContactPerson")
    WebElement contactPersonField;

    @FindBy(id = "TrainingVenue_EmailID")
    WebElement emailIdField;

    @FindBy(id = "TrainingVenue_Address2")
    WebElement address2Field;

    @FindBy(id = "TrainingVenue_State")
    WebElement stateField;

    @FindBy(id = "TrainingVenue_RoomEquipment")
    WebElement roomEquipmentField;

    @FindBy(id = "TrainingVenue_AdditionalInfo")
    WebElement additionalInfoField;

    // Alternative locators using common EPIQ patterns
    @FindBy(xpath = "//input[@id='VenueName'] | //input[@name='VenueName']")
    WebElement venueNameAlt;

    @FindBy(xpath = "//input[@id='RoomNumber'] | //input[@name='RoomNumber']")
    WebElement roomNumberAlt;

    @FindBy(xpath = "//input[@id='Capacity'] | //input[@name='Capacity']")
    WebElement capacityAlt;

    @FindBy(xpath = "//input[@id='PhoneNumber'] | //input[@name='PhoneNumber']")
    WebElement phoneNumberAlt;

    @FindBy(xpath = "//input[@id='UniqueCode'] | //input[@name='UniqueCode']")
    WebElement uniqueCodeAlt;

    @FindBy(xpath = "//input[@id='ContactPerson'] | //input[@name='ContactPerson']")
    WebElement contactPersonAlt;
    
    // Buttons - Multiple locator strategies
    @FindBy(xpath = "//button[text()='Submit'] | //input[@type='submit'] | //button[@id='btnSubmit'] | //input[@value='Submit']")
    WebElement submitButton;

    @FindBy(xpath = "//button[text()='View Existing'] | //button[contains(text(),'View')] | //a[contains(text(),'View')]")
    WebElement viewExistingButton;

    // Alternative locators (backup) - Multiple strategies
    @FindBy(xpath = "//input[@type='text' or not(@type)] | //input[@class='form-control']")
    WebElement firstFormField;

    @FindBy(xpath = "(//input[@type='text' or not(@type)])[2] | (//input[@class='form-control'])[2]")
    WebElement secondFormField;

    // Generic locators for dynamic form finding
    @FindBy(xpath = "//form//input | //div[contains(@class,'form')]//input | //table//input")
    WebElement anyInputField;

    @FindBy(xpath = "//form//textarea | //div[contains(@class,'form')]//textarea | //table//textarea")
    WebElement anyTextAreaField;
    
    // Constructor
    public CM_TrainingVenueRegistration() {
        PageFactory.initElements(driver, this);
    }
    
    // Individual field methods following existing project patterns
    public void enterTrainingVenueName(String venueName) {
        sendKeys2(trainingVenueNameField, "Enter Training Venue Name", venueName,
                CommonStrings.sendKeys_AC.getCommonStrings(), 
                CommonStrings.sendKeys_AR.getCommonStrings(),
                "Training Venue Name field");
    }
    
    public void enterRoomNumber(String roomNumber) {
        sendKeys2(roomNumberField, "Enter Room Number", roomNumber,
                CommonStrings.sendKeys_AC.getCommonStrings(),
                CommonStrings.sendKeys_AR.getCommonStrings(),
                "Room Number field");
    }
    
    public void enterCapacity(String capacity) {
        sendKeys2(capacityField, "Enter Capacity", capacity,
                CommonStrings.sendKeys_AC.getCommonStrings(),
                CommonStrings.sendKeys_AR.getCommonStrings(),
                "Capacity field");
    }
    
    public void enterPhoneNumber(String phoneNumber) {
        sendKeys2(phoneNumberField, "Enter Phone Number", phoneNumber,
                CommonStrings.sendKeys_AC.getCommonStrings(),
                CommonStrings.sendKeys_AR.getCommonStrings(),
                "Phone Number field");
    }
    
    public void enterAddress1(String address1) {
        sendKeys2(address1Field, "Enter Address1", address1,
                CommonStrings.sendKeys_AC.getCommonStrings(),
                CommonStrings.sendKeys_AR.getCommonStrings(),
                "Address1 field");
    }
    
    public void enterCity(String city) {
        sendKeys2(cityField, "Enter City", city,
                CommonStrings.sendKeys_AC.getCommonStrings(),
                CommonStrings.sendKeys_AR.getCommonStrings(),
                "City field");
    }
    
    public void enterPinZip(String pinZip) {
        sendKeys2(pinZipField, "Enter Pin/Zip", pinZip,
                CommonStrings.sendKeys_AC.getCommonStrings(),
                CommonStrings.sendKeys_AR.getCommonStrings(),
                "Pin/Zip field");
    }
    
    public void enterCostPerHour(String costPerHour) {
        sendKeys2(costPerHourField, "Enter Cost Per Hour", costPerHour,
                CommonStrings.sendKeys_AC.getCommonStrings(),
                CommonStrings.sendKeys_AR.getCommonStrings(),
                "Cost Per Hour field");
    }
    
    public void enterUniqueCode(String uniqueCode) {
        sendKeys2(uniqueCodeField, "Enter Unique Code", uniqueCode,
                CommonStrings.sendKeys_AC.getCommonStrings(),
                CommonStrings.sendKeys_AR.getCommonStrings(),
                "Unique Code field");
    }
    
    public void enterRoomType(String roomType) {
        sendKeys2(roomTypeField, "Enter Room Type", roomType,
                CommonStrings.sendKeys_AC.getCommonStrings(),
                CommonStrings.sendKeys_AR.getCommonStrings(),
                "Room Type field");
    }
    
    public void enterContactPerson(String contactPerson) {
        sendKeys2(contactPersonField, "Enter Contact Person", contactPerson,
                CommonStrings.sendKeys_AC.getCommonStrings(),
                CommonStrings.sendKeys_AR.getCommonStrings(),
                "Contact Person field");
    }
    
    public void enterEmailId(String emailId) {
        sendKeys2(emailIdField, "Enter Email ID", emailId,
                CommonStrings.sendKeys_AC.getCommonStrings(),
                CommonStrings.sendKeys_AR.getCommonStrings(),
                "Email ID field");
    }
    
    public void enterAddress2(String address2) {
        sendKeys2(address2Field, "Enter Address2", address2,
                CommonStrings.sendKeys_AC.getCommonStrings(),
                CommonStrings.sendKeys_AR.getCommonStrings(),
                "Address2 field");
    }
    
    public void enterState(String state) {
        sendKeys2(stateField, "Enter State", state,
                CommonStrings.sendKeys_AC.getCommonStrings(),
                CommonStrings.sendKeys_AR.getCommonStrings(),
                "State field");
    }
    
    public void enterRoomEquipment(String roomEquipment) {
        sendKeys2(roomEquipmentField, "Enter Room Equipment", roomEquipment,
                CommonStrings.sendKeys_AC.getCommonStrings(),
                CommonStrings.sendKeys_AR.getCommonStrings(),
                "Room Equipment field");
    }
    
    public void enterAdditionalInfo(String additionalInfo) {
        sendKeys2(additionalInfoField, "Enter Additional Information", additionalInfo,
                CommonStrings.sendKeys_AC.getCommonStrings(),
                CommonStrings.sendKeys_AR.getCommonStrings(),
                "Additional Information field");
    }
    
    public void clickSubmit() {
        click2(submitButton, CommonStrings.Submit_Button_DC.getCommonStrings(),
                CommonStrings.Submit_Button_AC.getCommonStrings(),
                CommonStrings.Submit_Button_AR.getCommonStrings(),
                CommonStrings.Submit_Button_SS.getCommonStrings());
    }
    
    public void clickViewExisting() {
        click2(viewExistingButton, "Click on View Existing button",
                "View Existing button should be clicked",
                "View Existing button is clicked",
                "View Existing button");
    }
    
    // Complete form filling method
    public void fillTrainingVenueForm(HashMap<String, String> testData) {
        // Fill mandatory fields
        enterTrainingVenueName(testData.get("trainingVenueName"));
        TimeUtil.shortWait();
        
        enterRoomNumber(testData.get("roomNumber"));
        TimeUtil.shortWait();
        
        enterCapacity(testData.get("capacity"));
        TimeUtil.shortWait();
        
        enterPhoneNumber(testData.get("phoneNumber"));
        TimeUtil.shortWait();
        
        enterUniqueCode(testData.get("uniqueCode"));
        TimeUtil.shortWait();
        
        enterRoomType(testData.get("roomType"));
        TimeUtil.shortWait();
        
        enterContactPerson(testData.get("contactPerson"));
        TimeUtil.shortWait();
        
        // Fill optional fields if provided
        if (testData.get("address1") != null && !testData.get("address1").isEmpty()) {
            enterAddress1(testData.get("address1"));
            TimeUtil.shortWait();
        }
        
        if (testData.get("city") != null && !testData.get("city").isEmpty()) {
            enterCity(testData.get("city"));
            TimeUtil.shortWait();
        }
        
        if (testData.get("pinZip") != null && !testData.get("pinZip").isEmpty()) {
            enterPinZip(testData.get("pinZip"));
            TimeUtil.shortWait();
        }
        
        if (testData.get("costPerHour") != null && !testData.get("costPerHour").isEmpty()) {
            enterCostPerHour(testData.get("costPerHour"));
            TimeUtil.shortWait();
        }
        
        if (testData.get("emailId") != null && !testData.get("emailId").isEmpty()) {
            enterEmailId(testData.get("emailId"));
            TimeUtil.shortWait();
        }
        
        if (testData.get("address2") != null && !testData.get("address2").isEmpty()) {
            enterAddress2(testData.get("address2"));
            TimeUtil.shortWait();
        }
        
        if (testData.get("state") != null && !testData.get("state").isEmpty()) {
            enterState(testData.get("state"));
            TimeUtil.shortWait();
        }
        
        if (testData.get("roomEquipment") != null && !testData.get("roomEquipment").isEmpty()) {
            enterRoomEquipment(testData.get("roomEquipment"));
            TimeUtil.shortWait();
        }
        
        if (testData.get("additionalInfo") != null && !testData.get("additionalInfo").isEmpty()) {
            enterAdditionalInfo(testData.get("additionalInfo"));
            TimeUtil.shortWait();
        }
    }
    
    // Submit form method
    public void submitTrainingVenueForm(HashMap<String, String> testData) {
        fillTrainingVenueForm(testData);
        TimeUtil.mediumWait();
        clickSubmit();
    }
}
