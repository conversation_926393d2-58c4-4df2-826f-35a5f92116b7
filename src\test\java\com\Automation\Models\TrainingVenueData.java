package com.Automation.Models;

/**
 * Training Venue Data Model
 * Simple POJO for test data
 */
public class TrainingVenueData {
    
    // Mandatory fields
    private String venueName;
    private String roomNumber;
    private String capacity;
    private String phoneNumber;
    private String uniqueCode;
    private String roomType;
    private String contactPerson;
    
    // Optional fields
    private String address1;
    private String city;
    private String pinZip;
    private String costPerHour;
    private String emailId;
    private String address2;
    private String state;
    private String roomEquipment;
    private String additionalInfo;
    
    // Constructor for mandatory fields
    public TrainingVenueData(String venueName, String roomNumber, String capacity, 
                           String phoneNumber, String uniqueCode, String roomType, 
                           String contactPerson) {
        this.venueName = venueName;
        this.roomNumber = roomNumber;
        this.capacity = capacity;
        this.phoneNumber = phoneNumber;
        this.uniqueCode = uniqueCode;
        this.roomType = roomType;
        this.contactPerson = contactPerson;
    }
    
    // Default constructor
    public TrainingVenueData() {}
    
    // Getters and Setters
    public String getVenueName() { return venueName; }
    public void setVenueName(String venueName) { this.venueName = venueName; }
    
    public String getRoomNumber() { return roomNumber; }
    public void setRoomNumber(String roomNumber) { this.roomNumber = roomNumber; }
    
    public String getCapacity() { return capacity; }
    public void setCapacity(String capacity) { this.capacity = capacity; }
    
    public String getPhoneNumber() { return phoneNumber; }
    public void setPhoneNumber(String phoneNumber) { this.phoneNumber = phoneNumber; }
    
    public String getUniqueCode() { return uniqueCode; }
    public void setUniqueCode(String uniqueCode) { this.uniqueCode = uniqueCode; }
    
    public String getRoomType() { return roomType; }
    public void setRoomType(String roomType) { this.roomType = roomType; }
    
    public String getContactPerson() { return contactPerson; }
    public void setContactPerson(String contactPerson) { this.contactPerson = contactPerson; }
    
    public String getAddress1() { return address1; }
    public void setAddress1(String address1) { this.address1 = address1; }
    
    public String getCity() { return city; }
    public void setCity(String city) { this.city = city; }
    
    public String getPinZip() { return pinZip; }
    public void setPinZip(String pinZip) { this.pinZip = pinZip; }
    
    public String getCostPerHour() { return costPerHour; }
    public void setCostPerHour(String costPerHour) { this.costPerHour = costPerHour; }
    
    public String getEmailId() { return emailId; }
    public void setEmailId(String emailId) { this.emailId = emailId; }
    
    public String getAddress2() { return address2; }
    public void setAddress2(String address2) { this.address2 = address2; }
    
    public String getState() { return state; }
    public void setState(String state) { this.state = state; }
    
    public String getRoomEquipment() { return roomEquipment; }
    public void setRoomEquipment(String roomEquipment) { this.roomEquipment = roomEquipment; }
    
    public String getAdditionalInfo() { return additionalInfo; }
    public void setAdditionalInfo(String additionalInfo) { this.additionalInfo = additionalInfo; }
    
    // Static factory methods for common test data
    public static TrainingVenueData createBasicVenue() {
        return new TrainingVenueData(
            "Conference Hall A",
            "Room-101", 
            "50",
            "9876543210",
            "VENUE001",
            "Conference Room",
            "John Doe"
        );
    }
    
    public static TrainingVenueData createCompleteVenue() {
        TrainingVenueData venue = new TrainingVenueData(
            "Training Center B",
            "Room-102",
            "25", 
            "9876543211",
            "VENUE002",
            "Training Room",
            "Jane Smith"
        );
        
        venue.setAddress1("123 Business Park");
        venue.setCity("New York");
        venue.setPinZip("10001");
        venue.setCostPerHour("100");
        venue.setEmailId("<EMAIL>");
        venue.setAddress2("Building A, Floor 2");
        venue.setState("NY");
        venue.setRoomEquipment("Projector, Whiteboard, Audio System");
        venue.setAdditionalInfo("Air conditioned room with parking facility");
        
        return venue;
    }
    
    @Override
    public String toString() {
        return "TrainingVenueData{" +
                "venueName='" + venueName + '\'' +
                ", roomNumber='" + roomNumber + '\'' +
                ", capacity='" + capacity + '\'' +
                ", uniqueCode='" + uniqueCode + '\'' +
                ", contactPerson='" + contactPerson + '\'' +
                '}';
    }
}
