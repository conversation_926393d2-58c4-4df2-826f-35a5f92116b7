package com.Automation.Tests;

import org.openqa.selenium.WebDriver;
import org.openqa.selenium.chrome.ChromeDriver;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import com.Automation.Pages.TrainingVenuePage;
import com.Automation.Models.TrainingVenueData;

/**
 * Enhanced Training Venue Test with Data Model
 */
public class TrainingVenueTestWithData {
    
    private WebDriver driver;
    private TrainingVenuePage trainingVenuePage;
    
    @BeforeMethod
    public void setUp() {
        driver = new ChromeDriver();
        driver.manage().window().maximize();
        driver.get("http://cqmsconfig05/EPIQ/Pages/MainPage?TaskId=#");
        
        try {
            Thread.sleep(3000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        
        trainingVenuePage = new TrainingVenuePage(driver);
    }
    
    @Test(priority = 1)
    public void testBasicVenueRegistration() {
        System.out.println("=== Test: Basic Venue Registration ===");
        
        // Create test data
        TrainingVenueData venueData = TrainingVenueData.createBasicVenue();
        
        // Fill form using data model
        fillFormWithData(venueData);
        
        // Submit
        trainingVenuePage.submitForm();
        
        System.out.println("=== Basic Test Completed ===");
    }
    
    @Test(priority = 2)
    public void testCompleteVenueRegistration() {
        System.out.println("=== Test: Complete Venue Registration ===");
        
        // Create complete test data
        TrainingVenueData venueData = TrainingVenueData.createCompleteVenue();
        
        // Fill form using data model
        fillFormWithData(venueData);
        fillOptionalFieldsWithData(venueData);
        
        // Submit
        trainingVenuePage.submitForm();
        
        System.out.println("=== Complete Test Completed ===");
    }
    
    @Test(priority = 3)
    public void testCustomVenueRegistration() {
        System.out.println("=== Test: Custom Venue Registration ===");
        
        // Create custom test data
        TrainingVenueData venueData = new TrainingVenueData();
        venueData.setVenueName("Executive Boardroom");
        venueData.setRoomNumber("Room-Executive-01");
        venueData.setCapacity("12");
        venueData.setPhoneNumber("9876543213");
        venueData.setUniqueCode("EXEC001");
        venueData.setRoomType("Executive Boardroom");
        venueData.setContactPerson("Robert Wilson");
        venueData.setEmailId("<EMAIL>");
        venueData.setCity("Chicago");
        venueData.setState("IL");
        venueData.setCostPerHour("200");
        venueData.setRoomEquipment("4K Display, Video Conferencing, Executive Seating");
        venueData.setAdditionalInfo("Premium executive boardroom with butler service");
        
        // Fill form
        fillFormWithData(venueData);
        fillOptionalFieldsWithData(venueData);
        
        // Submit
        trainingVenuePage.submitForm();
        
        System.out.println("=== Custom Test Completed ===");
    }
    
    // Helper method to fill mandatory fields
    private void fillFormWithData(TrainingVenueData data) {
        trainingVenuePage.enterVenueName(data.getVenueName());
        trainingVenuePage.enterRoomNumber(data.getRoomNumber());
        trainingVenuePage.enterCapacity(data.getCapacity());
        trainingVenuePage.enterPhoneNumber(data.getPhoneNumber());
        trainingVenuePage.enterUniqueCode(data.getUniqueCode());
        trainingVenuePage.enterRoomType(data.getRoomType());
        trainingVenuePage.enterContactPerson(data.getContactPerson());
    }
    
    // Helper method to fill optional fields
    private void fillOptionalFieldsWithData(TrainingVenueData data) {
        if (data.getAddress1() != null) {
            trainingVenuePage.enterAddress1(data.getAddress1());
        }
        if (data.getCity() != null) {
            trainingVenuePage.enterCity(data.getCity());
        }
        if (data.getPinZip() != null) {
            trainingVenuePage.enterPinZip(data.getPinZip());
        }
        if (data.getCostPerHour() != null) {
            trainingVenuePage.enterCostPerHour(data.getCostPerHour());
        }
        if (data.getEmailId() != null) {
            trainingVenuePage.enterEmailId(data.getEmailId());
        }
        if (data.getAddress2() != null) {
            trainingVenuePage.enterAddress2(data.getAddress2());
        }
        if (data.getState() != null) {
            trainingVenuePage.enterState(data.getState());
        }
        if (data.getRoomEquipment() != null) {
            trainingVenuePage.enterRoomEquipment(data.getRoomEquipment());
        }
        if (data.getAdditionalInfo() != null) {
            trainingVenuePage.enterAdditionalInfo(data.getAdditionalInfo());
        }
    }
    
    @AfterMethod
    public void tearDown() {
        try {
            Thread.sleep(5000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        // driver.quit();
    }
}

/**
 * Simple standalone test class for quick execution
 */
class SimpleTrainingVenueTest {
    
    public static void main(String[] args) {
        WebDriver driver = new ChromeDriver();
        
        try {
            // Setup
            driver.manage().window().maximize();
            driver.get("http://cqmsconfig05/EPIQ/Pages/MainPage?TaskId=#");
            Thread.sleep(3000);
            
            // Initialize page
            TrainingVenuePage page = new TrainingVenuePage(driver);
            
            // Test 1: Quick test with mandatory fields
            System.out.println("=== Quick Test ===");
            page.fillCompleteForm(
                "Quick Test Room",
                "QT-001", 
                "30",
                "1234567890",
                "QUICK001",
                "Test Room",
                "Test User"
            );
            page.submitForm();
            
            Thread.sleep(5000);
            
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            // driver.quit();
        }
    }
}
