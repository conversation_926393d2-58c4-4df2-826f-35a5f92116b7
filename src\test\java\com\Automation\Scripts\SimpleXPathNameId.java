package com.Automation.Scripts;

import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.chrome.ChromeDriver;

/**
 * Simple Training Venue Script using XPath with name/id attributes
 * Most reliable approach with fallback options
 */
public class SimpleXPathNameId {
    
    public static void main(String[] args) {
        WebDriver driver = new ChromeDriver();
        
        try {
            // Navigate to application
            driver.get("http://cqmsconfig05/EPIQ/Pages/MainPage?TaskId=#");
            driver.manage().window().maximize();
            Thread.sleep(3000);
            
            System.out.println("=== Training Venue Registration - XPath with Name/ID ===");
            
            // Fill mandatory fields using XPath with name/id
            fillField(driver, "//input[@name='VenueName' or @id='VenueName']", "Conference Hall A", "Venue Name");
            fillField(driver, "//input[@name='RoomNumber' or @id='RoomNumber']", "Room-101", "Room Number");
            fillField(driver, "//input[@name='Capacity' or @id='Capacity']", "50", "Capacity");
            fillField(driver, "//input[@name='PhoneNumber' or @id='PhoneNumber']", "9876543210", "Phone Number");
            fillField(driver, "//input[@name='UniqueCode' or @id='UniqueCode']", "VENUE001", "Unique Code");
            fillField(driver, "//input[@name='RoomType' or @id='RoomType']", "Conference Room", "Room Type");
            fillField(driver, "//input[@name='ContactPerson' or @id='ContactPerson']", "John Doe", "Contact Person");
            
            // Fill optional fields
            fillField(driver, "//input[@name='Address1' or @id='Address1']", "123 Business Park", "Address1");
            fillField(driver, "//input[@name='City' or @id='City']", "New York", "City");
            fillField(driver, "//input[@name='PinZip' or @id='PinZip']", "10001", "Pin/Zip");
            fillField(driver, "//input[@name='CostPerHour' or @id='CostPerHour']", "100", "Cost Per Hour");
            fillField(driver, "//input[@name='EmailID' or @id='EmailID']", "<EMAIL>", "Email ID");
            fillField(driver, "//input[@name='Address2' or @id='Address2']", "Building A, Floor 2", "Address2");
            fillField(driver, "//input[@name='State' or @id='State']", "NY", "State");
            
            // Fill textarea fields
            fillField(driver, "//textarea[@name='RoomEquipment' or @id='RoomEquipment']", "Projector, Whiteboard, Audio System", "Room Equipment");
            fillField(driver, "//textarea[@name='AdditionalInfo' or @id='AdditionalInfo']", "Air conditioned room with parking facility", "Additional Info");
            
            // Submit form
            submitForm(driver);
            
            System.out.println("=== Form Submitted Successfully ===");
            Thread.sleep(5000);
            
        } catch (Exception e) {
            System.out.println("Error: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // driver.quit(); // Uncomment to close browser
        }
    }
    
    /**
     * Fill field using XPath locator
     */
    private static void fillField(WebDriver driver, String xpath, String value, String fieldName) {
        try {
            WebElement field = driver.findElement(By.xpath(xpath));
            
            if (field.isDisplayed() && field.isEnabled()) {
                field.clear();
                field.sendKeys(value);
                System.out.println("✓ " + fieldName + ": " + value);
                Thread.sleep(200);
            }
            
        } catch (Exception e) {
            System.out.println("✗ Failed to fill " + fieldName + ": " + e.getMessage());
        }
    }
    
    /**
     * Submit form using multiple XPath strategies
     */
    private static void submitForm(WebDriver driver) {
        try {
            // Multiple submit button XPath locators
            String[] submitXPaths = {
                "//button[text()='Submit']",
                "//input[@type='submit']",
                "//button[@id='btnSubmit']",
                "//input[@value='Submit']",
                "//button[contains(text(),'Submit')]",
                "//button[@name='Submit' or @id='Submit']"
            };
            
            for (String xpath : submitXPaths) {
                try {
                    WebElement submitBtn = driver.findElement(By.xpath(xpath));
                    if (submitBtn.isDisplayed() && submitBtn.isEnabled()) {
                        submitBtn.click();
                        System.out.println("✓ Submit button clicked using: " + xpath);
                        return;
                    }
                } catch (Exception e) {
                    continue;
                }
            }
            
            System.out.println("✗ Could not find submit button");
            
        } catch (Exception e) {
            System.out.println("✗ Submit failed: " + e.getMessage());
        }
    }
}

/**
 * Alternative XPath approaches
 */
class XPathAlternatives {
    
    /**
     * Using contains() for partial matching
     */
    public static void containsApproach(WebDriver driver) {
        try {
            driver.findElement(By.xpath("//input[contains(@name,'Venue') or contains(@id,'Venue')]")).sendKeys("Conference Hall A");
            driver.findElement(By.xpath("//input[contains(@name,'Room') or contains(@id,'Room')]")).sendKeys("Room-101");
            driver.findElement(By.xpath("//input[contains(@name,'Capacity') or contains(@id,'Capacity')]")).sendKeys("50");
            driver.findElement(By.xpath("//input[contains(@name,'Phone') or contains(@id,'Phone')]")).sendKeys("9876543210");
            driver.findElement(By.xpath("//input[contains(@name,'Unique') or contains(@id,'Unique')]")).sendKeys("VENUE001");
            driver.findElement(By.xpath("//input[contains(@name,'Contact') or contains(@id,'Contact')]")).sendKeys("John Doe");
            
            // Submit
            driver.findElement(By.xpath("//button[text()='Submit'] | //input[@type='submit']")).click();
            
        } catch (Exception e) {
            System.out.println("Contains approach failed: " + e.getMessage());
        }
    }
    
    /**
     * Using starts-with() for prefix matching
     */
    public static void startsWithApproach(WebDriver driver) {
        try {
            driver.findElement(By.xpath("//input[starts-with(@name,'Venue') or starts-with(@id,'Venue')]")).sendKeys("Conference Hall A");
            driver.findElement(By.xpath("//input[starts-with(@name,'Room') or starts-with(@id,'Room')]")).sendKeys("Room-101");
            driver.findElement(By.xpath("//input[starts-with(@name,'Capacity') or starts-with(@id,'Capacity')]")).sendKeys("50");
            
            // Submit
            driver.findElement(By.xpath("//button[text()='Submit']")).click();
            
        } catch (Exception e) {
            System.out.println("Starts-with approach failed: " + e.getMessage());
        }
    }
    
    /**
     * Using multiple attribute combinations
     */
    public static void multiAttributeApproach(WebDriver driver) {
        try {
            // Try multiple attribute combinations
            driver.findElement(By.xpath("//input[@name='VenueName' or @id='VenueName' or @class='venue-name']")).sendKeys("Conference Hall A");
            driver.findElement(By.xpath("//input[@name='RoomNumber' or @id='RoomNumber' or @class='room-number']")).sendKeys("Room-101");
            driver.findElement(By.xpath("//input[@name='Capacity' or @id='Capacity' or @class='capacity']")).sendKeys("50");
            
            // Submit with multiple options
            driver.findElement(By.xpath("//button[text()='Submit'] | //input[@type='submit'] | //button[@id='btnSubmit'] | //input[@value='Submit']")).click();
            
        } catch (Exception e) {
            System.out.println("Multi-attribute approach failed: " + e.getMessage());
        }
    }
    
    /**
     * Copy-paste ready XPath locators
     */
    public static void copyPasteXPaths(WebDriver driver) {
        try {
            // Ready-to-use XPath locators
            driver.findElement(By.xpath("//input[@name='VenueName' or @id='VenueName']")).sendKeys("Conference Hall A");
            driver.findElement(By.xpath("//input[@name='RoomNumber' or @id='RoomNumber']")).sendKeys("Room-101");
            driver.findElement(By.xpath("//input[@name='Capacity' or @id='Capacity']")).sendKeys("50");
            driver.findElement(By.xpath("//input[@name='PhoneNumber' or @id='PhoneNumber']")).sendKeys("9876543210");
            driver.findElement(By.xpath("//input[@name='UniqueCode' or @id='UniqueCode']")).sendKeys("VENUE001");
            driver.findElement(By.xpath("//input[@name='RoomType' or @id='RoomType']")).sendKeys("Conference Room");
            driver.findElement(By.xpath("//input[@name='ContactPerson' or @id='ContactPerson']")).sendKeys("John Doe");
            
            // Submit
            driver.findElement(By.xpath("//button[text()='Submit'] | //input[@type='submit']")).click();
            
        } catch (Exception e) {
            System.out.println("Copy-paste approach failed: " + e.getMessage());
        }
    }
}
