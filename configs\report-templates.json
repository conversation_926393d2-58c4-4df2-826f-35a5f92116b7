{"actions": {"click": {"templates": {"button": {"stepDescription": "Click on '{elementName}' button", "acceptanceCriteria": "'{elementName}' button should be clicked and {expectedAction}", "actualResult": "'{elementName}' button is getting clicked and {expectedAction}"}, "link": {"stepDescription": "Click on '{elementName}' link", "acceptanceCriteria": "'{elementName}' link should be clicked and navigation should occur", "actualResult": "'{elementName}' link is getting clicked and navigation is occurring"}, "menu": {"stepDescription": "Click on '{elementName}' menu item", "acceptanceCriteria": "'{elementName}' menu should be selected and submenu should be displayed", "actualResult": "'{elementName}' menu is getting selected and submenu is displayed"}, "default": {"stepDescription": "Click on '{elementName}' {elementType}", "acceptanceCriteria": "'{elementName}' should be clicked successfully", "actualResult": "'{elementName}' is getting clicked successfully"}}}, "sendKeys": {"templates": {"textField": {"stepDescription": "Enter '{inputValue}' in '{fieldName}' field", "acceptanceCriteria": "Entered value should be displayed in '{fieldName}' field", "actualResult": "Entered value is getting displayed in '{fieldName}' field"}, "passwordField": {"stepDescription": "Enter password in '{fieldName}' field", "acceptanceCriteria": "Password should be entered and masked in '{fieldName}' field", "actualResult": "Password is getting entered and masked in '{fieldName}' field"}, "emailField": {"stepDescription": "Enter email '{inputValue}' in '{fieldName}' field", "acceptanceCriteria": "Valid email should be entered in '{fieldName}' field", "actualResult": "Valid email is getting entered in '{fieldName}' field"}, "searchField": {"stepDescription": "Search for '{inputValue}' in '{fieldName}' field", "acceptanceCriteria": "Search term should be entered and search should be initiated", "actualResult": "Search term is getting entered and search is initiated"}, "default": {"stepDescription": "Enter '{inputValue}' in '{fieldName}' {fieldType}", "acceptanceCriteria": "Value should be entered in '{fieldName}' {fieldType}", "actualResult": "Value is getting entered in '{fieldName}' {fieldType}"}}}, "select": {"templates": {"dropdown": {"stepDescription": "Select '{optionValue}' from '{dropdownName}' dropdown", "acceptanceCriteria": "'{optionValue}' should be selected from dropdown", "actualResult": "'{optionValue}' is getting selected from dropdown"}}}, "verify": {"templates": {"text": {"stepDescription": "Verify '{expectedText}' is displayed in '{elementName}'", "acceptanceCriteria": "'{expectedText}' should be displayed in '{elementName}'", "actualResult": "'{expectedText}' is displayed in '{elementName}'"}, "presence": {"stepDescription": "Verify '{elementName}' is present on the page", "acceptanceCriteria": "'{elementName}' should be present and visible", "actualResult": "'{elementName}' is present and visible"}}}}, "elementTypes": {"button": ["button", "input[type=button]", "input[type=submit]", ".btn", "[role=button]"], "link": ["a", "[role=link]"], "textField": ["input[type=text]", "input:not([type])", ".form-control"], "passwordField": ["input[type=password]"], "emailField": ["input[type=email]"], "searchField": ["input[type=search]", "[placeholder*=search]", ".search-input"], "dropdown": ["select", ".dropdown", "[role=combobox]"], "menu": [".menu-item", ".nav-item", "[role=menuitem]"]}, "contextualRules": {"login": {"keywords": ["login", "signin", "sign-in", "authenticate"], "templates": {"stepDescription": "Enter login credentials and authenticate user", "acceptanceCriteria": "User should be authenticated and redirected to dashboard", "actualResult": "User is getting authenticated and redirected to dashboard"}}, "registration": {"keywords": ["register", "signup", "sign-up", "create-account"], "templates": {"stepDescription": "Register new user with provided details", "acceptanceCriteria": "New user should be registered successfully", "actualResult": "New user is getting registered successfully"}}, "search": {"keywords": ["search", "find", "lookup", "filter"], "templates": {"stepDescription": "Search for '{searchTerm}' in the system", "acceptanceCriteria": "Search results should be displayed for '{searchTerm}'", "actualResult": "Search results are getting displayed for '{searchTerm}'"}}}, "screenshotNaming": {"patterns": {"action_element": "{action}_{elementName}", "step_action": "step_{stepNumber}_{action}", "timestamp_action": "{timestamp}_{action}_{elementName}"}, "default": "action_element"}}