package com.Automation.Utils;

import java.sql.*;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

/**
 * Database-driven Report Content Provider
 * Stores and retrieves report templates from database
 */
public class DatabaseReportProvider {
    
    private static Connection connection;
    private static final Map<String, ReportTemplate> templateCache = new HashMap<>();
    
    static {
        initializeDatabase();
        loadTemplatesIntoCache();
    }
    
    private static void initializeDatabase() {
        try {
            // Using H2 in-memory database for this example
            connection = DriverManager.getConnection("jdbc:h2:mem:reportdb", "sa", "");
            createTables();
            insertDefaultTemplates();
        } catch (SQLException e) {
            System.err.println("Failed to initialize database: " + e.getMessage());
        }
    }
    
    private static void createTables() throws SQLException {
        String createTemplatesTable = """
            CREATE TABLE IF NOT EXISTS report_templates (
                id INT PRIMARY KEY AUTO_INCREMENT,
                action_type VARCHAR(50) NOT NULL,
                element_type VARCHAR(50),
                context VARCHAR(100),
                step_description_template TEXT,
                acceptance_criteria_template TEXT,
                actual_result_template TEXT,
                screenshot_name_template VARCHAR(200),
                priority INT DEFAULT 1,
                is_active BOOLEAN DEFAULT TRUE,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """;
        
        String createContextRulesTable = """
            CREATE TABLE IF NOT EXISTS context_rules (
                id INT PRIMARY KEY AUTO_INCREMENT,
                context_name VARCHAR(100) NOT NULL,
                keywords TEXT,
                business_value TEXT,
                user_story_template TEXT,
                tags TEXT,
                is_active BOOLEAN DEFAULT TRUE
            )
        """;
        
        String createElementMappingsTable = """
            CREATE TABLE IF NOT EXISTS element_mappings (
                id INT PRIMARY KEY AUTO_INCREMENT,
                element_selector VARCHAR(200),
                element_name VARCHAR(100),
                element_type VARCHAR(50),
                business_context VARCHAR(100),
                expected_action TEXT,
                is_active BOOLEAN DEFAULT TRUE
            )
        """;
        
        try (Statement stmt = connection.createStatement()) {
            stmt.execute(createTemplatesTable);
            stmt.execute(createContextRulesTable);
            stmt.execute(createElementMappingsTable);
        }
    }
    
    private static void insertDefaultTemplates() throws SQLException {
        String insertTemplate = """
            INSERT INTO report_templates 
            (action_type, element_type, context, step_description_template, 
             acceptance_criteria_template, actual_result_template, screenshot_name_template, priority)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """;
        
        try (PreparedStatement pstmt = connection.prepareStatement(insertTemplate)) {
            // Click templates
            pstmt.setString(1, "click");
            pstmt.setString(2, "button");
            pstmt.setString(3, "general");
            pstmt.setString(4, "Click on '{elementName}' button");
            pstmt.setString(5, "'{elementName}' button should be clicked and {expectedAction}");
            pstmt.setString(6, "'{elementName}' button is getting clicked and {expectedAction}");
            pstmt.setString(7, "click_{elementName}");
            pstmt.setInt(8, 1);
            pstmt.executeUpdate();
            
            // SendKeys templates
            pstmt.setString(1, "sendKeys");
            pstmt.setString(2, "input");
            pstmt.setString(3, "general");
            pstmt.setString(4, "Enter '{inputValue}' in '{fieldName}' field");
            pstmt.setString(5, "Value should be entered in '{fieldName}' field successfully");
            pstmt.setString(6, "Value is getting entered in '{fieldName}' field successfully");
            pstmt.setString(7, "input_{fieldName}");
            pstmt.setInt(8, 1);
            pstmt.executeUpdate();
            
            // Login context
            pstmt.setString(1, "click");
            pstmt.setString(2, "button");
            pstmt.setString(3, "login");
            pstmt.setString(4, "Click on '{elementName}' to authenticate user");
            pstmt.setString(5, "User should be authenticated and redirected to dashboard");
            pstmt.setString(6, "User is getting authenticated and redirected to dashboard");
            pstmt.setString(7, "login_authentication");
            pstmt.setInt(8, 2);
            pstmt.executeUpdate();
        }
        
        // Insert context rules
        String insertContextRule = """
            INSERT INTO context_rules (context_name, keywords, business_value, user_story_template, tags)
            VALUES (?, ?, ?, ?, ?)
        """;
        
        try (PreparedStatement pstmt = connection.prepareStatement(insertContextRule)) {
            pstmt.setString(1, "login");
            pstmt.setString(2, "login,signin,authenticate,credentials");
            pstmt.setString(3, "Enables user access to application features");
            pstmt.setString(4, "As a {userType}, I want to login so that I can access my account");
            pstmt.setString(5, "authentication,security,access");
            pstmt.executeUpdate();
            
            pstmt.setString(1, "registration");
            pstmt.setString(2, "register,signup,create,account");
            pstmt.setString(3, "Allows new users to join the platform");
            pstmt.setString(4, "As a new user, I want to register so that I can use the application");
            pstmt.setString(5, "registration,onboarding,user-management");
            pstmt.executeUpdate();
        }
    }
    
    private static void loadTemplatesIntoCache() {
        String query = """
            SELECT action_type, element_type, context, step_description_template,
                   acceptance_criteria_template, actual_result_template, 
                   screenshot_name_template, priority
            FROM report_templates 
            WHERE is_active = TRUE 
            ORDER BY priority DESC
        """;
        
        try (Statement stmt = connection.createStatement();
             ResultSet rs = stmt.executeQuery(query)) {
            
            while (rs.next()) {
                String key = rs.getString("action_type") + "_" + 
                           rs.getString("element_type") + "_" + 
                           rs.getString("context");
                
                ReportTemplate template = new ReportTemplate(
                    rs.getString("step_description_template"),
                    rs.getString("acceptance_criteria_template"),
                    rs.getString("actual_result_template"),
                    rs.getString("screenshot_name_template"),
                    rs.getInt("priority")
                );
                
                templateCache.put(key, template);
            }
        } catch (SQLException e) {
            System.err.println("Failed to load templates: " + e.getMessage());
        }
    }
    
    public static ReportTemplate getTemplate(String actionType, String elementType, String context) {
        // Try specific context first
        String key = actionType + "_" + elementType + "_" + context;
        ReportTemplate template = templateCache.get(key);
        
        if (template == null) {
            // Fallback to general context
            key = actionType + "_" + elementType + "_general";
            template = templateCache.get(key);
        }
        
        if (template == null) {
            // Final fallback to default template
            template = getDefaultTemplate(actionType);
        }
        
        return template;
    }
    
    private static ReportTemplate getDefaultTemplate(String actionType) {
        return new ReportTemplate(
            "Perform " + actionType + " on {elementName}",
            "Action should be completed successfully",
            "Action is getting completed successfully",
            actionType + "_{elementName}",
            0
        );
    }
    
    public static String generateContent(String template, Map<String, String> variables) {
        String result = template;
        for (Map.Entry<String, String> entry : variables.entrySet()) {
            result = result.replace("{" + entry.getKey() + "}", entry.getValue());
        }
        return result;
    }
    
    public static void addCustomTemplate(String actionType, String elementType, String context,
                                       String stepTemplate, String criteriaTemplate, 
                                       String resultTemplate, String screenshotTemplate) {
        String insertSQL = """
            INSERT INTO report_templates 
            (action_type, element_type, context, step_description_template,
             acceptance_criteria_template, actual_result_template, screenshot_name_template)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """;
        
        try (PreparedStatement pstmt = connection.prepareStatement(insertSQL)) {
            pstmt.setString(1, actionType);
            pstmt.setString(2, elementType);
            pstmt.setString(3, context);
            pstmt.setString(4, stepTemplate);
            pstmt.setString(5, criteriaTemplate);
            pstmt.setString(6, resultTemplate);
            pstmt.setString(7, screenshotTemplate);
            pstmt.executeUpdate();
            
            // Refresh cache
            templateCache.clear();
            loadTemplatesIntoCache();
            
        } catch (SQLException e) {
            System.err.println("Failed to add custom template: " + e.getMessage());
        }
    }
    
    public static ContextRule getContextRule(String contextName) {
        String query = "SELECT * FROM context_rules WHERE context_name = ? AND is_active = TRUE";
        
        try (PreparedStatement pstmt = connection.prepareStatement(query)) {
            pstmt.setString(1, contextName);
            ResultSet rs = pstmt.executeQuery();
            
            if (rs.next()) {
                return new ContextRule(
                    rs.getString("context_name"),
                    rs.getString("keywords"),
                    rs.getString("business_value"),
                    rs.getString("user_story_template"),
                    rs.getString("tags")
                );
            }
        } catch (SQLException e) {
            System.err.println("Failed to get context rule: " + e.getMessage());
        }
        
        return null;
    }
    
    // Data classes
    public static class ReportTemplate {
        public final String stepDescriptionTemplate;
        public final String acceptanceCriteriaTemplate;
        public final String actualResultTemplate;
        public final String screenshotNameTemplate;
        public final int priority;
        
        public ReportTemplate(String stepDescriptionTemplate, String acceptanceCriteriaTemplate,
                            String actualResultTemplate, String screenshotNameTemplate, int priority) {
            this.stepDescriptionTemplate = stepDescriptionTemplate;
            this.acceptanceCriteriaTemplate = acceptanceCriteriaTemplate;
            this.actualResultTemplate = actualResultTemplate;
            this.screenshotNameTemplate = screenshotNameTemplate;
            this.priority = priority;
        }
    }
    
    public static class ContextRule {
        public final String contextName;
        public final String keywords;
        public final String businessValue;
        public final String userStoryTemplate;
        public final String tags;
        
        public ContextRule(String contextName, String keywords, String businessValue,
                          String userStoryTemplate, String tags) {
            this.contextName = contextName;
            this.keywords = keywords;
            this.businessValue = businessValue;
            this.userStoryTemplate = userStoryTemplate;
            this.tags = tags;
        }
    }
}
