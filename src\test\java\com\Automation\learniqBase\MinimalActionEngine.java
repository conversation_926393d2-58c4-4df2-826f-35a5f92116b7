package com.Automation.learniqBase;

import org.openqa.selenium.WebElement;
import com.Automation.Utils.BasicHTMLLogger;

/**
 * Minimal Action Engine - Simplest possible reporting
 * Just logs basic actions to HTML without any complex formatting
 */
public class MinimalActionEngine extends TestEngine {
    
    public MinimalActionEngine() {
        super();
    }
    
    public MinimalActionEngine(String url) {
        super(url);
    }
    
    /**
     * Click with minimal logging
     */
    public void click(WebElement element) {
        try {
            element.click();
            BasicHTMLLogger.logStep("Clicked element", true);
        } catch (Exception e) {
            BasicHTMLLogger.logStep("Failed to click element", false);
            throw e;
        }
    }
    
    /**
     * Click with custom message
     */
    public void click(WebElement element, String message) {
        try {
            element.click();
            BasicHTMLLogger.logStep("Clicked: " + message, true);
        } catch (Exception e) {
            BasicHTMLLogger.logStep("Failed to click: " + message, false);
            throw e;
        }
    }
    
    /**
     * SendKeys with minimal logging
     */
    public void type(WebElement element, String text) {
        try {
            element.clear();
            element.sendKeys(text);
            BasicHTMLLogger.logStep("Entered text: " + text, true);
        } catch (Exception e) {
            BasicHTMLLogger.logStep("Failed to enter text: " + text, false);
            throw e;
        }
    }
    
    /**
     * SendKeys with custom message
     */
    public void type(WebElement element, String text, String fieldName) {
        try {
            element.clear();
            element.sendKeys(text);
            BasicHTMLLogger.logStep("Entered '" + text + "' in " + fieldName, true);
        } catch (Exception e) {
            BasicHTMLLogger.logStep("Failed to enter text in " + fieldName, false);
            throw e;
        }
    }
    
    /**
     * Navigate with logging
     */
    public void navigate(String url) {
        try {
            driver.get(url);
            BasicHTMLLogger.logStep("Navigated to: " + url, true);
        } catch (Exception e) {
            BasicHTMLLogger.logStep("Failed to navigate to: " + url, false);
            throw e;
        }
    }
    
    /**
     * Verify text with logging
     */
    public void verifyText(WebElement element, String expectedText) {
        try {
            String actualText = element.getText();
            boolean passed = actualText.equals(expectedText);
            if (passed) {
                BasicHTMLLogger.logStep("Verified text: " + expectedText, true);
            } else {
                BasicHTMLLogger.logStep("Text verification failed. Expected: " + expectedText + ", Actual: " + actualText, false);
                throw new AssertionError("Text mismatch");
            }
        } catch (Exception e) {
            BasicHTMLLogger.logStep("Failed to verify text: " + expectedText, false);
            throw e;
        }
    }
    
    /**
     * Log custom action
     */
    public void logAction(String action, boolean success) {
        BasicHTMLLogger.logStep(action, success);
    }
    
    /**
     * Log info message
     */
    public void logInfo(String message) {
        BasicHTMLLogger.logInfo(message);
    }
}
