package com.Automation.Scripts;

import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.chrome.ChromeDriver;

/**
 * SIMPLE TRAINING VENUE SCRIPT - NAME/ID LOCATORS ONLY
 * 
 * Uses only name and id attributes for locating elements
 */
public class SimpleNameIdScript {
    
    public static void main(String[] args) {
        WebDriver driver = new ChromeDriver();
        
        try {
            // Navigate to page
            driver.get("http://cqmsconfig05/EPIQ/Pages/MainPage?TaskId=#");
            driver.manage().window().maximize();
            Thread.sleep(3000);
            
            System.out.println("=== Filling Training Venue Form ===");
            
            // Fill form using name/id attributes
            fillByName(driver, "VenueName", "Conference Hall A");
            fillByName(driver, "RoomNumber", "Room-101");
            fillByName(driver, "Capacity", "50");
            fillByName(driver, "PhoneNumber", "9876543210");
            fillByName(driver, "Address1", "123 Business Park");
            fillByName(driver, "City", "New York");
            fillByName(driver, "PinZip", "10001");
            fillByName(driver, "CostPerHour", "100");
            fillByName(driver, "UniqueCode", "VENUE001");
            fillByName(driver, "RoomType", "Conference Room");
            fillByName(driver, "ContactPerson", "John Doe");
            fillByName(driver, "EmailID", "<EMAIL>");
            fillByName(driver, "Address2", "Building A, Floor 2");
            fillByName(driver, "State", "NY");
            fillByName(driver, "RoomEquipment", "Projector, Whiteboard, Audio System");
            fillByName(driver, "AdditionalInfo", "Air conditioned room with parking facility");
            
            // Submit form
            clickById(driver, "btnSubmit");
            
            System.out.println("=== Form Submitted ===");
            Thread.sleep(5000);
            
        } catch (Exception e) {
            System.out.println("Error: " + e.getMessage());
        } finally {
            // driver.quit();
        }
    }
    
    /**
     * Fill field by name attribute
     */
    private static void fillByName(WebDriver driver, String name, String value) {
        try {
            WebElement field = driver.findElement(By.name(name));
            field.clear();
            field.sendKeys(value);
            System.out.println("✓ " + name + ": " + value);
            Thread.sleep(200);
        } catch (Exception e) {
            // Try by id if name fails
            try {
                WebElement field = driver.findElement(By.id(name));
                field.clear();
                field.sendKeys(value);
                System.out.println("✓ " + name + " (by ID): " + value);
                Thread.sleep(200);
            } catch (Exception e2) {
                System.out.println("✗ Failed: " + name);
            }
        }
    }
    
    /**
     * Click button by id
     */
    private static void clickById(WebDriver driver, String id) {
        try {
            WebElement button = driver.findElement(By.id(id));
            button.click();
            System.out.println("✓ Clicked: " + id);
        } catch (Exception e) {
            // Try by name if id fails
            try {
                WebElement button = driver.findElement(By.name(id));
                button.click();
                System.out.println("✓ Clicked: " + id + " (by name)");
            } catch (Exception e2) {
                System.out.println("✗ Failed to click: " + id);
            }
        }
    }
}

/**
 * ALTERNATIVE SIMPLE APPROACHES
 */
class AlternativeNameIdApproaches {
    
    /**
     * Approach 1: Direct ID locators
     */
    public static void fillByDirectId(WebDriver driver) {
        try {
            driver.findElement(By.id("VenueName")).sendKeys("Conference Hall A");
            driver.findElement(By.id("RoomNumber")).sendKeys("Room-101");
            driver.findElement(By.id("Capacity")).sendKeys("50");
            driver.findElement(By.id("PhoneNumber")).sendKeys("9876543210");
            driver.findElement(By.id("UniqueCode")).sendKeys("VENUE001");
            driver.findElement(By.id("ContactPerson")).sendKeys("John Doe");
            driver.findElement(By.id("btnSubmit")).click();
        } catch (Exception e) {
            System.out.println("Direct ID approach failed: " + e.getMessage());
        }
    }
    
    /**
     * Approach 2: Direct name locators
     */
    public static void fillByDirectName(WebDriver driver) {
        try {
            driver.findElement(By.name("VenueName")).sendKeys("Conference Hall A");
            driver.findElement(By.name("RoomNumber")).sendKeys("Room-101");
            driver.findElement(By.name("Capacity")).sendKeys("50");
            driver.findElement(By.name("PhoneNumber")).sendKeys("9876543210");
            driver.findElement(By.name("UniqueCode")).sendKeys("VENUE001");
            driver.findElement(By.name("ContactPerson")).sendKeys("John Doe");
            driver.findElement(By.name("Submit")).click();
        } catch (Exception e) {
            System.out.println("Direct name approach failed: " + e.getMessage());
        }
    }
    
    /**
     * Approach 3: Prefixed ID patterns (common in enterprise apps)
     */
    public static void fillByPrefixedId(WebDriver driver) {
        try {
            driver.findElement(By.id("TrainingVenue_VenueName")).sendKeys("Conference Hall A");
            driver.findElement(By.id("TrainingVenue_RoomNumber")).sendKeys("Room-101");
            driver.findElement(By.id("TrainingVenue_Capacity")).sendKeys("50");
            driver.findElement(By.id("TrainingVenue_PhoneNumber")).sendKeys("9876543210");
            driver.findElement(By.id("TrainingVenue_UniqueCode")).sendKeys("VENUE001");
            driver.findElement(By.id("TrainingVenue_ContactPerson")).sendKeys("John Doe");
            driver.findElement(By.id("btnSubmit")).click();
        } catch (Exception e) {
            System.out.println("Prefixed ID approach failed: " + e.getMessage());
        }
    }
}
