package com.Automation.Utils;

import com.aventstack.extentreports.ExtentReports;
import com.aventstack.extentreports.ExtentTest;
import com.aventstack.extentreports.Status;
import com.aventstack.extentreports.reporter.ExtentSparkReporter;
import com.aventstack.extentreports.reporter.configuration.Theme;
import org.openqa.selenium.OutputType;
import org.openqa.selenium.TakesScreenshot;
import org.openqa.selenium.WebDriver;

import java.io.IOException;
import java.net.InetAddress;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * Simple ExtentReports - Clean and minimal reporting
 * Shows only test results, failure reasons, and screenshots
 */
public class SimpleExtentReporter {
    
    private static ExtentReports extent;
    private static ExtentTest currentTest;
    private static WebDriver driver;
    private static String reportPath;
    
    /**
     * Initialize the simple extent reporter
     */
    public static void initializeReport(String testSuiteName, WebDriver webDriver) {
        driver = webDriver;
        
        // Create report path with timestamp
        SimpleDateFormat dateFormat = new SimpleDateFormat("dd-MMM-yyyy_HH-mm-ss");
        String timestamp = dateFormat.format(new Date());
        reportPath = System.getProperty("user.dir") + "/test-output/SimpleReport_" + timestamp + ".html";
        
        // Initialize ExtentReports
        extent = new ExtentReports();
        ExtentSparkReporter sparkReporter = new ExtentSparkReporter(reportPath);
        
        // Configure the reporter
        sparkReporter.config().setTheme(Theme.STANDARD);
        sparkReporter.config().setDocumentTitle("Test Execution Report");
        sparkReporter.config().setReportName(testSuiteName);
        sparkReporter.config().setTimeStampFormat("dd/MM/yyyy hh:mm:ss");
        
        // Attach reporter
        extent.attachReporter(sparkReporter);
        
        // Set system information
        try {
            InetAddress address = InetAddress.getLocalHost();
            extent.setSystemInfo("Application", "LearnIQ Automation");
            extent.setSystemInfo("Browser", ConfigsReader.getPropValue("browser"));
            extent.setSystemInfo("OS", System.getProperty("os.name"));
            extent.setSystemInfo("Host Name", address.getHostName());
            extent.setSystemInfo("Environment", "Test");
            extent.setSystemInfo("Executed By", System.getProperty("user.name"));
        } catch (Exception e) {
            System.err.println("Failed to set system info: " + e.getMessage());
        }
    }
    
    /**
     * Start a new test
     */
    public static void startTest(String testName) {
        currentTest = extent.createTest(testName);
        System.out.println("Started test: " + testName);
    }
    
    /**
     * Start a new test with description
     */
    public static void startTest(String testName, String description) {
        currentTest = extent.createTest(testName, description);
        System.out.println("Started test: " + testName);
    }
    
    /**
     * Log test pass
     */
    public static void logPass(String message) {
        if (currentTest != null) {
            currentTest.pass(message);
        }
    }
    
    /**
     * Log test failure with screenshot
     */
    public static void logFail(String message) {
        if (currentTest != null) {
            currentTest.fail(message);
            attachScreenshot("Failure Screenshot");
        }
    }
    
    /**
     * Log test failure with exception and screenshot
     */
    public static void logFail(String message, Throwable exception) {
        if (currentTest != null) {
            currentTest.fail(message);
            currentTest.fail(exception);
            attachScreenshot("Failure Screenshot");
        }
    }
    
    /**
     * Log info message
     */
    public static void logInfo(String message) {
        if (currentTest != null) {
            currentTest.info(message);
        }
    }
    
    /**
     * Log warning message
     */
    public static void logWarning(String message) {
        if (currentTest != null) {
            currentTest.warning(message);
        }
    }
    
    /**
     * Log skip message
     */
    public static void logSkip(String message) {
        if (currentTest != null) {
            currentTest.skip(message);
        }
    }
    
    /**
     * Attach screenshot to current test
     */
    public static void attachScreenshot(String screenshotName) {
        if (currentTest != null && driver != null) {
            try {
                TakesScreenshot takesScreenshot = (TakesScreenshot) driver;
                String base64Screenshot = takesScreenshot.getScreenshotAs(OutputType.BASE64);
                currentTest.addScreenCaptureFromBase64String(base64Screenshot, screenshotName);
            } catch (Exception e) {
                System.err.println("Failed to attach screenshot: " + e.getMessage());
            }
        }
    }
    
    /**
     * Mark current test as passed
     */
    public static void markTestPassed() {
        if (currentTest != null) {
            currentTest.pass("Test completed successfully");
        }
    }
    
    /**
     * Mark current test as failed
     */
    public static void markTestFailed(String reason) {
        if (currentTest != null) {
            currentTest.fail("Test failed: " + reason);
            attachScreenshot("Test Failure");
        }
    }
    
    /**
     * Mark current test as skipped
     */
    public static void markTestSkipped(String reason) {
        if (currentTest != null) {
            currentTest.skip("Test skipped: " + reason);
        }
    }
    
    /**
     * Add test step without detailed table
     */
    public static void logStep(String stepDescription) {
        if (currentTest != null) {
            currentTest.info("✓ " + stepDescription);
        }
    }
    
    /**
     * Add failed step without detailed table
     */
    public static void logFailedStep(String stepDescription, String errorMessage) {
        if (currentTest != null) {
            currentTest.fail("✗ " + stepDescription + " - " + errorMessage);
            attachScreenshot("Step Failure");
        }
    }
    
    /**
     * Finalize and flush the report
     */
    public static void finalizeReport() {
        if (extent != null) {
            extent.flush();
            System.out.println("Report generated: " + reportPath);
        }
    }
    
    /**
     * Get current test instance (for advanced usage)
     */
    public static ExtentTest getCurrentTest() {
        return currentTest;
    }
    
    /**
     * Check if reporter is initialized
     */
    public static boolean isInitialized() {
        return extent != null;
    }
}
