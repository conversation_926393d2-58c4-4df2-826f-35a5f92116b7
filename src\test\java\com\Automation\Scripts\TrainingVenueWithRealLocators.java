package com.Automation.Scripts;

import org.openqa.selenium.By;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.openqa.selenium.support.ui.ExpectedConditions;
import java.time.Duration;
import java.util.List;

/**
 * Training Venue Registration with Real EPIQ Application Locators
 * Based on actual ID/Name patterns found in the codebase
 */
public class TrainingVenueWithRealLocators {
    
    private static WebDriver driver;
    private static WebDriverWait wait;
    private static JavascriptExecutor js;
    
    // Test data
    private static final String[][] FIELD_DATA = {
        // {fieldName, testValue, primaryId, alternativeId, nameAttribute, xpathLocator}
        {"Training Venue Name", "Conference Hall A", "TrainingVenue_VenueName", "VenueName", "VenueName", "//input[contains(@id,'Venue') or contains(@name,'Venue')]"},
        {"Room Number", "Room-101", "TrainingVenue_RoomNumber", "RoomNumber", "RoomNumber", "//input[contains(@id,'Room') or contains(@name,'Room')]"},
        {"Capacity", "50", "TrainingVenue_Capacity", "Capacity", "Capacity", "//input[contains(@id,'Capacity') or contains(@name,'Capacity')]"},
        {"Phone Number", "9876543210", "TrainingVenue_PhoneNumber", "PhoneNumber", "PhoneNumber", "//input[contains(@id,'Phone') or contains(@name,'Phone')]"},
        {"Address1", "123 Business Park", "TrainingVenue_Address1", "Address1", "Address1", "//input[contains(@id,'Address1') or contains(@name,'Address1')]"},
        {"City", "New York", "TrainingVenue_City", "City", "City", "//input[contains(@id,'City') or contains(@name,'City')]"},
        {"Pin/Zip", "10001", "TrainingVenue_PinZip", "PinZip", "PinZip", "//input[contains(@id,'Pin') or contains(@name,'Pin') or contains(@id,'Zip')]"},
        {"Cost Per Hour", "100", "TrainingVenue_CostPerHour", "CostPerHour", "CostPerHour", "//input[contains(@id,'Cost') or contains(@name,'Cost')]"},
        {"Unique Code", "VENUE001", "TrainingVenue_UniqueCode", "UniqueCode", "UniqueCode", "//input[contains(@id,'Unique') or contains(@name,'Unique')]"},
        {"Room Type", "Conference Room", "TrainingVenue_RoomType", "RoomType", "RoomType", "//input[contains(@id,'Type') or contains(@name,'Type')]"},
        {"Contact Person", "John Doe", "TrainingVenue_ContactPerson", "ContactPerson", "ContactPerson", "//input[contains(@id,'Contact') or contains(@name,'Contact')]"},
        {"Email ID", "<EMAIL>", "TrainingVenue_EmailID", "EmailID", "EmailID", "//input[contains(@id,'Email') or contains(@name,'Email')]"},
        {"Address2", "Building A, Floor 2", "TrainingVenue_Address2", "Address2", "Address2", "//input[contains(@id,'Address2') or contains(@name,'Address2')]"},
        {"State", "NY", "TrainingVenue_State", "State", "State", "//input[contains(@id,'State') or contains(@name,'State')]"}
    };
    
    private static final String[][] TEXTAREA_DATA = {
        {"Room Equipment", "Projector, Whiteboard, Audio System", "TrainingVenue_RoomEquipment", "RoomEquipment", "//textarea[contains(@id,'Equipment') or contains(@name,'Equipment')]"},
        {"Additional Info", "Air conditioned room with parking facility", "TrainingVenue_AdditionalInfo", "AdditionalInfo", "//textarea[contains(@id,'Additional') or contains(@name,'Additional')]"}
    };
    
    public static void main(String[] args) {
        try {
            initializeDriver();
            navigateToPage();
            
            System.out.println("=== Training Venue Registration with Real Locators ===");
            
            // Fill input fields
            fillInputFields();
            
            // Fill textarea fields
            fillTextAreaFields();
            
            // Submit form
            submitForm();
            
            System.out.println("=== Form submission completed ===");
            Thread.sleep(5000);
            
        } catch (Exception e) {
            System.out.println("Error: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // Uncomment to close browser
            // driver.quit();
        }
    }
    
    private static void initializeDriver() {
        driver = new ChromeDriver();
        wait = new WebDriverWait(driver, Duration.ofSeconds(15));
        js = (JavascriptExecutor) driver;
        driver.manage().window().maximize();
    }
    
    private static void navigateToPage() {
        driver.get("http://cqmsconfig05/EPIQ/Pages/MainPage?TaskId=#");
        wait.until(ExpectedConditions.jsReturnsValue("return document.readyState === 'complete'"));
        
        try {
            Thread.sleep(3000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    private static void fillInputFields() {
        System.out.println("Filling input fields...");
        
        for (String[] fieldData : FIELD_DATA) {
            String fieldName = fieldData[0];
            String testValue = fieldData[1];
            String primaryId = fieldData[2];
            String alternativeId = fieldData[3];
            String nameAttribute = fieldData[4];
            String xpathLocator = fieldData[5];
            
            boolean filled = fillFieldWithMultipleStrategies(fieldName, testValue, primaryId, alternativeId, nameAttribute, xpathLocator);
            
            if (filled) {
                System.out.println("✓ Filled " + fieldName + ": " + testValue);
            } else {
                System.out.println("✗ Failed to fill " + fieldName);
            }
            
            try {
                Thread.sleep(300);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
    }
    
    private static void fillTextAreaFields() {
        System.out.println("Filling textarea fields...");
        
        for (String[] textareaData : TEXTAREA_DATA) {
            String fieldName = textareaData[0];
            String testValue = textareaData[1];
            String primaryId = textareaData[2];
            String alternativeId = textareaData[3];
            String xpathLocator = textareaData[4];
            
            boolean filled = fillTextAreaWithMultipleStrategies(fieldName, testValue, primaryId, alternativeId, xpathLocator);
            
            if (filled) {
                System.out.println("✓ Filled " + fieldName + ": " + testValue);
            } else {
                System.out.println("✗ Failed to fill " + fieldName);
            }
            
            try {
                Thread.sleep(300);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
    }
    
    private static boolean fillFieldWithMultipleStrategies(String fieldName, String value, String primaryId, String altId, String nameAttr, String xpath) {
        // Strategy 1: Primary ID
        if (fillByLocator(By.id(primaryId), value, "ID: " + primaryId)) {
            return true;
        }
        
        // Strategy 2: Alternative ID
        if (fillByLocator(By.id(altId), value, "Alt ID: " + altId)) {
            return true;
        }
        
        // Strategy 3: Name attribute
        if (fillByLocator(By.name(nameAttr), value, "Name: " + nameAttr)) {
            return true;
        }
        
        // Strategy 4: XPath
        if (fillByLocator(By.xpath(xpath), value, "XPath: " + xpath)) {
            return true;
        }
        
        // Strategy 5: Label-based locator
        String labelXpath = "//label[contains(text(),'" + fieldName + "')]/following-sibling::input | //label[contains(text(),'" + fieldName + "')]/..//input";
        if (fillByLocator(By.xpath(labelXpath), value, "Label-based: " + fieldName)) {
            return true;
        }
        
        // Strategy 6: Partial text matching
        String partialXpath = "//input[contains(@id,'" + fieldName.split(" ")[0] + "') or contains(@name,'" + fieldName.split(" ")[0] + "')]";
        if (fillByLocator(By.xpath(partialXpath), value, "Partial match: " + fieldName.split(" ")[0])) {
            return true;
        }
        
        return false;
    }
    
    private static boolean fillTextAreaWithMultipleStrategies(String fieldName, String value, String primaryId, String altId, String xpath) {
        // Strategy 1: Primary ID
        if (fillByLocator(By.id(primaryId), value, "Textarea ID: " + primaryId)) {
            return true;
        }
        
        // Strategy 2: Alternative ID
        if (fillByLocator(By.id(altId), value, "Textarea Alt ID: " + altId)) {
            return true;
        }
        
        // Strategy 3: XPath
        if (fillByLocator(By.xpath(xpath), value, "Textarea XPath: " + xpath)) {
            return true;
        }
        
        // Strategy 4: Generic textarea locators
        String[] genericLocators = {
            "//textarea[contains(@id,'" + fieldName.split(" ")[0] + "')]",
            "//textarea[contains(@name,'" + fieldName.split(" ")[0] + "')]",
            "//label[contains(text(),'" + fieldName + "')]/following-sibling::textarea",
            "//label[contains(text(),'" + fieldName + "')]/..//textarea"
        };
        
        for (String locator : genericLocators) {
            if (fillByLocator(By.xpath(locator), value, "Generic textarea: " + locator)) {
                return true;
            }
        }
        
        return false;
    }
    
    private static boolean fillByLocator(By locator, String value, String strategy) {
        try {
            WebElement element = driver.findElement(locator);
            if (element.isDisplayed() && element.isEnabled()) {
                // Scroll to element
                js.executeScript("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", element);
                Thread.sleep(200);
                
                // Clear and fill
                element.clear();
                element.sendKeys(value);
                
                // Trigger events
                js.executeScript("arguments[0].dispatchEvent(new Event('input', { bubbles: true }));", element);
                js.executeScript("arguments[0].dispatchEvent(new Event('change', { bubbles: true }));", element);
                
                return true;
            }
        } catch (Exception e) {
            // Element not found or not interactable
        }
        return false;
    }
    
    private static void submitForm() {
        System.out.println("Attempting to submit form...");
        
        // Multiple submit button strategies based on EPIQ patterns
        String[] submitStrategies = {
            "btnSubmit",                                    // Common EPIQ pattern
            "Submit",                                       // Simple ID
            "TrainingVenue_Submit",                         // Prefixed pattern
            "//button[@id='btnSubmit']",                    // XPath with ID
            "//input[@type='submit']",                      // Submit input
            "//button[text()='Submit']",                    // Button text
            "//input[@value='Submit']",                     // Input value
            "//button[contains(@class,'submit')]",          // CSS class
            "//button[contains(text(),'Submit')]",          // Partial text
            "//form//button[last()]",                       // Last button in form
            "//div[contains(@class,'button')]//button"      // Button in div
        };
        
        for (String strategy : submitStrategies) {
            try {
                WebElement submitBtn = null;
                
                // Try as ID first, then as XPath
                if (!strategy.startsWith("//")) {
                    try {
                        submitBtn = driver.findElement(By.id(strategy));
                    } catch (Exception e) {
                        // Not found by ID, skip
                        continue;
                    }
                } else {
                    try {
                        submitBtn = driver.findElement(By.xpath(strategy));
                    } catch (Exception e) {
                        // Not found by XPath, skip
                        continue;
                    }
                }
                
                if (submitBtn != null && submitBtn.isDisplayed() && submitBtn.isEnabled()) {
                    js.executeScript("arguments[0].scrollIntoView(true);", submitBtn);
                    Thread.sleep(500);
                    
                    // Try click, then JavaScript click if needed
                    try {
                        submitBtn.click();
                    } catch (Exception e) {
                        js.executeScript("arguments[0].click();", submitBtn);
                    }
                    
                    System.out.println("✓ Submit button clicked using: " + strategy);
                    return;
                }
            } catch (Exception e) {
                continue;
            }
        }
        
        // Last resort: Try to submit any form on the page
        try {
            js.executeScript("document.querySelector('form').submit();");
            System.out.println("✓ Form submitted using JavaScript");
        } catch (Exception e) {
            System.out.println("✗ Could not submit form with any strategy");
        }
    }
}
