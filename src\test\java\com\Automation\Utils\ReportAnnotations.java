package com.Automation.Utils;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Annotation-based Report Content Definition
 */
public class ReportAnnotations {
    
    @Retention(RetentionPolicy.RUNTIME)
    @Target(ElementType.FIELD)
    public @interface ReportElement {
        String name();
        String description() default "";
        ElementType type() default ElementType.BUTTON;
        String expectedAction() default "";
        String businessContext() default "";
    }
    
    @Retention(RetentionPolicy.RUNTIME)
    @Target(ElementType.METHOD)
    public @interface TestStep {
        String description();
        String expectedResult();
        int priority() default 1;
        String[] tags() default {};
        String businessValue() default "";
    }
    
    @Retention(RetentionPolicy.RUNTIME)
    @Target(ElementType.METHOD)
    public @interface ReportTemplate {
        String stepTemplate() default "{action} on '{element}' {type}";
        String criteriaTemplate() default "'{element}' should {expectedAction}";
        String resultTemplate() default "'{element}' is {actualAction}";
    }
    
    public enum ElementType {
        BUTTON("button", "be clicked"),
        INPUT_FIELD("field", "accept input"),
        DROPDOWN("dropdown", "show options"),
        LINK("link", "navigate"),
        CHECKBOX("checkbox", "be selected/deselected"),
        RADIO_BUTTON("radio button", "be selected"),
        TEXT_AREA("text area", "accept text input"),
        MENU("menu", "expand and show options"),
        TAB("tab", "be activated"),
        MODAL("modal", "be displayed"),
        FORM("form", "be submitted");
        
        private final String displayName;
        private final String defaultAction;
        
        ElementType(String displayName, String defaultAction) {
            this.displayName = displayName;
            this.defaultAction = defaultAction;
        }
        
        public String getDisplayName() { return displayName; }
        public String getDefaultAction() { return defaultAction; }
    }
    
    @Retention(RetentionPolicy.RUNTIME)
    @Target(ElementType.TYPE)
    public @interface PageContext {
        String pageName();
        String module() default "";
        String businessFlow() default "";
        String userRole() default "";
    }
    
    @Retention(RetentionPolicy.RUNTIME)
    @Target(ElementType.FIELD)
    public @interface ValidationRule {
        String rule();
        String errorMessage() default "";
        String successMessage() default "";
    }
}
