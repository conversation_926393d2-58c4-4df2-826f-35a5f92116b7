package com.Automation.learniqObjects;

import java.util.HashMap;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import com.Automation.learniqBase.OQActionEngine;
import com.Automation.Strings.CommonStrings;
import com.Automation.Utils.TimeUtil;

public class CM_TrainingVenueRegistration_Contains extends OQActionEngine {

    // Left Column Fields - Using contains() in XPath
    @FindBy(xpath = "//input[contains(@name,'Venue') or contains(@id,'Venue')]")
    WebElement trainingVenueNameField;
    
    @FindBy(xpath = "//input[contains(@name,'Room') or contains(@id,'Room')]")
    WebElement roomNumberField;
    
    @FindBy(xpath = "//input[contains(@name,'Capacity') or contains(@id,'Capacity')]")
    WebElement capacityField;
    
    @FindBy(xpath = "//input[contains(@name,'Phone') or contains(@id,'Phone')]")
    WebElement phoneNumberField;
    
    @FindBy(xpath = "//input[contains(@name,'Address1') or contains(@id,'Address1')]")
    WebElement address1Field;
    
    @FindBy(xpath = "//input[contains(@name,'City') or contains(@id,'City')]")
    WebElement cityField;
    
    @FindBy(xpath = "//input[contains(@name,'Pin') or contains(@id,'Pin') or contains(@name,'Zip') or contains(@id,'Zip')]")
    WebElement pinZipField;
    
    @FindBy(xpath = "//input[contains(@name,'Cost') or contains(@id,'Cost')]")
    WebElement costPerHourField;
    
    // Right Column Fields
    @FindBy(xpath = "//input[contains(@name,'Unique') or contains(@id,'Unique') or contains(@name,'Code') or contains(@id,'Code')]")
    WebElement uniqueCodeField;
    
    @FindBy(xpath = "//input[contains(@name,'Type') or contains(@id,'Type')]")
    WebElement roomTypeField;
    
    @FindBy(xpath = "//input[contains(@name,'Contact') or contains(@id,'Contact') or contains(@name,'Person') or contains(@id,'Person')]")
    WebElement contactPersonField;
    
    @FindBy(xpath = "//input[contains(@name,'Email') or contains(@id,'Email')]")
    WebElement emailIdField;
    
    @FindBy(xpath = "//input[contains(@name,'Address2') or contains(@id,'Address2')]")
    WebElement address2Field;
    
    @FindBy(xpath = "//input[contains(@name,'State') or contains(@id,'State')]")
    WebElement stateField;
    
    @FindBy(xpath = "//textarea[contains(@name,'Equipment') or contains(@id,'Equipment')]")
    WebElement roomEquipmentField;
    
    @FindBy(xpath = "//textarea[contains(@name,'Additional') or contains(@id,'Additional') or contains(@name,'Info') or contains(@id,'Info')]")
    WebElement additionalInfoField;
    
    // Buttons
    @FindBy(xpath = "//button[contains(text(),'Submit')] | //input[@type='submit'] | //button[contains(@id,'Submit')]")
    WebElement submitButton;
    
    @FindBy(xpath = "//button[contains(text(),'View')] | //button[contains(@id,'View')]")
    WebElement viewExistingButton;
    
    // Constructor
    public CM_TrainingVenueRegistration_Contains() {
        PageFactory.initElements(driver, this);
    }
    
    // Complete form filling and submission method
    public void submitTrainingVenueForm(HashMap<String, String> testData) {
        // Fill mandatory fields
        sendKeys2(trainingVenueNameField, "Enter Training Venue Name", testData.get("trainingVenueName"),
                CommonStrings.sendKeys_AC.getCommonStrings(), 
                CommonStrings.sendKeys_AR.getCommonStrings(), "Training Venue Name field");
        TimeUtil.shortWait();
        
        sendKeys2(roomNumberField, "Enter Room Number", testData.get("roomNumber"),
                CommonStrings.sendKeys_AC.getCommonStrings(),
                CommonStrings.sendKeys_AR.getCommonStrings(), "Room Number field");
        TimeUtil.shortWait();
        
        sendKeys2(capacityField, "Enter Capacity", testData.get("capacity"),
                CommonStrings.sendKeys_AC.getCommonStrings(),
                CommonStrings.sendKeys_AR.getCommonStrings(), "Capacity field");
        TimeUtil.shortWait();
        
        sendKeys2(phoneNumberField, "Enter Phone Number", testData.get("phoneNumber"),
                CommonStrings.sendKeys_AC.getCommonStrings(),
                CommonStrings.sendKeys_AR.getCommonStrings(), "Phone Number field");
        TimeUtil.shortWait();
        
        sendKeys2(uniqueCodeField, "Enter Unique Code", testData.get("uniqueCode"),
                CommonStrings.sendKeys_AC.getCommonStrings(),
                CommonStrings.sendKeys_AR.getCommonStrings(), "Unique Code field");
        TimeUtil.shortWait();
        
        sendKeys2(roomTypeField, "Enter Room Type", testData.get("roomType"),
                CommonStrings.sendKeys_AC.getCommonStrings(),
                CommonStrings.sendKeys_AR.getCommonStrings(), "Room Type field");
        TimeUtil.shortWait();
        
        sendKeys2(contactPersonField, "Enter Contact Person", testData.get("contactPerson"),
                CommonStrings.sendKeys_AC.getCommonStrings(),
                CommonStrings.sendKeys_AR.getCommonStrings(), "Contact Person field");
        TimeUtil.shortWait();
        
        // Fill optional fields if provided
        if (testData.get("address1") != null && !testData.get("address1").isEmpty()) {
            sendKeys2(address1Field, "Enter Address1", testData.get("address1"),
                    CommonStrings.sendKeys_AC.getCommonStrings(),
                    CommonStrings.sendKeys_AR.getCommonStrings(), "Address1 field");
            TimeUtil.shortWait();
        }
        
        if (testData.get("city") != null && !testData.get("city").isEmpty()) {
            sendKeys2(cityField, "Enter City", testData.get("city"),
                    CommonStrings.sendKeys_AC.getCommonStrings(),
                    CommonStrings.sendKeys_AR.getCommonStrings(), "City field");
            TimeUtil.shortWait();
        }
        
        if (testData.get("pinZip") != null && !testData.get("pinZip").isEmpty()) {
            sendKeys2(pinZipField, "Enter Pin/Zip", testData.get("pinZip"),
                    CommonStrings.sendKeys_AC.getCommonStrings(),
                    CommonStrings.sendKeys_AR.getCommonStrings(), "Pin/Zip field");
            TimeUtil.shortWait();
        }
        
        if (testData.get("costPerHour") != null && !testData.get("costPerHour").isEmpty()) {
            sendKeys2(costPerHourField, "Enter Cost Per Hour", testData.get("costPerHour"),
                    CommonStrings.sendKeys_AC.getCommonStrings(),
                    CommonStrings.sendKeys_AR.getCommonStrings(), "Cost Per Hour field");
            TimeUtil.shortWait();
        }
        
        if (testData.get("emailId") != null && !testData.get("emailId").isEmpty()) {
            sendKeys2(emailIdField, "Enter Email ID", testData.get("emailId"),
                    CommonStrings.sendKeys_AC.getCommonStrings(),
                    CommonStrings.sendKeys_AR.getCommonStrings(), "Email ID field");
            TimeUtil.shortWait();
        }
        
        if (testData.get("address2") != null && !testData.get("address2").isEmpty()) {
            sendKeys2(address2Field, "Enter Address2", testData.get("address2"),
                    CommonStrings.sendKeys_AC.getCommonStrings(),
                    CommonStrings.sendKeys_AR.getCommonStrings(), "Address2 field");
            TimeUtil.shortWait();
        }
        
        if (testData.get("state") != null && !testData.get("state").isEmpty()) {
            sendKeys2(stateField, "Enter State", testData.get("state"),
                    CommonStrings.sendKeys_AC.getCommonStrings(),
                    CommonStrings.sendKeys_AR.getCommonStrings(), "State field");
            TimeUtil.shortWait();
        }
        
        if (testData.get("roomEquipment") != null && !testData.get("roomEquipment").isEmpty()) {
            sendKeys2(roomEquipmentField, "Enter Room Equipment", testData.get("roomEquipment"),
                    CommonStrings.sendKeys_AC.getCommonStrings(),
                    CommonStrings.sendKeys_AR.getCommonStrings(), "Room Equipment field");
            TimeUtil.shortWait();
        }
        
        if (testData.get("additionalInfo") != null && !testData.get("additionalInfo").isEmpty()) {
            sendKeys2(additionalInfoField, "Enter Additional Information", testData.get("additionalInfo"),
                    CommonStrings.sendKeys_AC.getCommonStrings(),
                    CommonStrings.sendKeys_AR.getCommonStrings(), "Additional Information field");
            TimeUtil.shortWait();
        }
        
        // Submit the form
        TimeUtil.mediumWait();
        click2(submitButton, CommonStrings.Submit_Button_DC.getCommonStrings(),
                CommonStrings.Submit_Button_AC.getCommonStrings(),
                CommonStrings.Submit_Button_AR.getCommonStrings(),
                CommonStrings.Submit_Button_SS.getCommonStrings());
    }
}
