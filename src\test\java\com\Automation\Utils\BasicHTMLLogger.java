package com.Automation.Utils;

import java.io.FileWriter;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * Basic HTML Logger - Simplest possible HTML reporting
 * Writes directly to HTML file without any complex structures
 */
public class BasicHTMLLogger {
    
    private static FileWriter writer;
    private static String reportPath = "./test-output/basic-report.html";
    private static int stepCounter = 0;
    private static boolean isInitialized = false;
    
    public static void startReport(String testName) {
        try {
            writer = new FileWriter(reportPath);
            stepCounter = 0;
            isInitialized = true;
            
            // Write HTML header
            writer.write("<!DOCTYPE html>\n<html>\n<head>\n");
            writer.write("<title>" + testName + "</title>\n");
            writer.write("<style>\n");
            writer.write("body { font-family: Arial; margin: 20px; }\n");
            writer.write(".pass { color: green; }\n");
            writer.write(".fail { color: red; }\n");
            writer.write(".step { margin: 5px 0; padding: 5px; border-left: 3px solid #ccc; }\n");
            writer.write("</style>\n");
            writer.write("</head>\n<body>\n");
            writer.write("<h1>" + testName + "</h1>\n");
            writer.write("<p>Started: " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "</p>\n");
            writer.flush();
            
        } catch (IOException e) {
            System.err.println("Failed to start report: " + e.getMessage());
        }
    }
    
    public static void logStep(String action, boolean passed) {
        if (!isInitialized) return;
        
        try {
            stepCounter++;
            String status = passed ? "PASS" : "FAIL";
            String cssClass = passed ? "pass" : "fail";
            String time = new SimpleDateFormat("HH:mm:ss").format(new Date());
            
            writer.write("<div class='step'>\n");
            writer.write("<strong>Step " + stepCounter + ":</strong> " + action + " ");
            writer.write("<span class='" + cssClass + "'>[" + status + "]</span> ");
            writer.write("<small>(" + time + ")</small>\n");
            writer.write("</div>\n");
            writer.flush();
            
        } catch (IOException e) {
            System.err.println("Failed to log step: " + e.getMessage());
        }
    }
    
    public static void logInfo(String message) {
        if (!isInitialized) return;
        
        try {
            writer.write("<p><em>" + message + "</em></p>\n");
            writer.flush();
        } catch (IOException e) {
            System.err.println("Failed to log info: " + e.getMessage());
        }
    }
    
    public static void endReport() {
        if (!isInitialized) return;
        
        try {
            writer.write("<p>Completed: " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "</p>\n");
            writer.write("<p>Total Steps: " + stepCounter + "</p>\n");
            writer.write("</body>\n</html>");
            writer.close();
            isInitialized = false;
            
            System.out.println("Basic HTML report generated: " + reportPath);
            
        } catch (IOException e) {
            System.err.println("Failed to end report: " + e.getMessage());
        }
    }
}
