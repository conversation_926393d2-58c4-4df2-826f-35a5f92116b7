package com.Automation.learniqBase;

import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.openqa.selenium.support.ui.ExpectedConditions;
import java.time.Duration;
import com.Automation.Utils.SimpleReportGenerator;

/**
 * Simple Action Engine with Basic HTML Reporting
 * Minimal reporting without complex tables or screenshots
 */
public class SimpleActionEngine extends TestEngine {
    
    public SimpleActionEngine() {
        super();
    }
    
    public SimpleActionEngine(String url) {
        super(url);
    }
    
    /**
     * Simple click with basic logging
     */
    public void simpleClick(WebElement element, String elementName) {
        try {
            waitForElement(element);
            element.click();
            SimpleReportGenerator.logAction("Click", elementName, true);
        } catch (Exception e) {
            SimpleReportGenerator.logAction("Click", elementName, false);
            throw e;
        }
    }
    
    /**
     * Simple sendKeys with basic logging
     */
    public void simpleSendKeys(WebElement element, String text, String fieldName) {
        try {
            waitForElement(element);
            element.clear();
            element.sendKeys(text);
            SimpleReportGenerator.logAction("Enter text: " + text, fieldName, true);
        } catch (Exception e) {
            SimpleReportGenerator.logAction("Enter text: " + text, fieldName, false);
            throw e;
        }
    }
    
    /**
     * Simple verification with basic logging
     */
    public void simpleVerify(WebElement element, String expectedText, String elementName) {
        try {
            waitForElement(element);
            String actualText = element.getText();
            boolean passed = actualText.equals(expectedText);
            SimpleReportGenerator.logAction("Verify text: " + expectedText, elementName, passed);
            if (!passed) {
                throw new AssertionError("Expected: " + expectedText + ", Actual: " + actualText);
            }
        } catch (Exception e) {
            SimpleReportGenerator.logAction("Verify text: " + expectedText, elementName, false);
            throw e;
        }
    }
    
    /**
     * Auto-detect element name and perform click
     */
    public void autoClick(WebElement element) {
        String elementName = getElementName(element);
        simpleClick(element, elementName);
    }
    
    /**
     * Auto-detect field name and perform sendKeys
     */
    public void autoSendKeys(WebElement element, String text) {
        String fieldName = getElementName(element);
        simpleSendKeys(element, text, fieldName);
    }
    
    /**
     * Log custom action
     */
    public void logAction(String action, boolean passed) {
        SimpleReportGenerator.logAction(action, passed);
    }
    
    /**
     * Log navigation
     */
    public void logNavigation(String url) {
        try {
            driver.get(url);
            SimpleReportGenerator.logAction("Navigate to: " + url, true);
        } catch (Exception e) {
            SimpleReportGenerator.logAction("Navigate to: " + url, false);
            throw e;
        }
    }
    
    /**
     * Wait for element to be visible
     */
    private void waitForElement(WebElement element) {
        WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(10));
        wait.until(ExpectedConditions.visibilityOf(element));
    }
    
    /**
     * Get element name from various attributes
     */
    private String getElementName(WebElement element) {
        try {
            // Try different attributes to get a meaningful name
            String name = element.getAttribute("data-testid");
            if (name != null && !name.isEmpty()) return name;
            
            name = element.getAttribute("id");
            if (name != null && !name.isEmpty()) return name;
            
            name = element.getAttribute("name");
            if (name != null && !name.isEmpty()) return name;
            
            name = element.getAttribute("placeholder");
            if (name != null && !name.isEmpty()) return name;
            
            name = element.getText();
            if (name != null && !name.isEmpty() && name.length() < 30) return name;
            
            return element.getTagName();
        } catch (Exception e) {
            return "Unknown Element";
        }
    }
}
