package com.Automation.Objects;

import java.util.HashMap;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;
import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.ExcelUtilUpdated;
import com.Automation.learniqBase.OQActionEngine;

public class TrainingVenueRegistration_Test extends OQActionEngine {

    String ExcelPath = "./learnIQTestData/TrainingVenue/TrainingVenueData.xlsx";
    ExcelUtilUpdated venueData = new ExcelUtilUpdated(ExcelPath, "VenueRegistration");

    public TrainingVenueRegistration_Test() {
        super(ConfigsReader.getPropValue("applicationUrl"));
    }

    @DataProvider(name = "TrainingVenueData")
    public Object[][] getVenueData() throws Exception {
        Object[][] obj = new Object[venueData.getRowCount()][1];
        for (int i = 1; i <= venueData.getRowCount(); i++) {
            HashMap<String, String> testData = venueData.getTestDataInMap(i);
            obj[i - 1][0] = testData;
        }
        return obj;
    }

    /**
     * Test method for Training Venue Registration
     * @param testData - Test data from Excel
     * @throws Throwable
     */
    @Test(priority = 1, dataProvider = "TrainingVenueData")
    public void trainingVenueRegistration(HashMap<String, String> testData) throws Throwable {

        if (isReportedRequired == true) {
            test = extent.createTest("Training Venue Registration")
                    .assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
                    .assignCategory("Training Venue Registration");
        }

        // Login to application
        epiclogin.loginToApplication(
                ConfigsReader.getPropValue("company"), 
                ConfigsReader.getPropValue("EpicUserID"),
                ConfigsReader.getPropValue("EpicUserPWD")
        );
        
        // Select plant
        epiclogin.plant1();

        // Navigate to Training Venue Registration and fill form
        trainingVenueReg.submitTrainingVenueForm(testData);

        // Logout
        Logout.signOutPage();
    }

    /**
     * Test method for Training Venue Registration with minimal data
     */
    @Test(priority = 2)
    public void trainingVenueRegistrationMinimal() throws Throwable {

        if (isReportedRequired == true) {
            test = extent.createTest("Training Venue Registration - Minimal Data")
                    .assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
                    .assignCategory("Training Venue Registration");
        }

        // Create minimal test data
        HashMap<String, String> testData = new HashMap<>();
        testData.put("trainingVenueName", "Conference Hall A");
        testData.put("roomNumber", "Room-101");
        testData.put("capacity", "50");
        testData.put("phoneNumber", "9876543210");
        testData.put("uniqueCode", "VENUE001");
        testData.put("roomType", "Conference Room");
        testData.put("contactPerson", "John Doe");

        // Login to application
        epiclogin.loginToApplication(
                ConfigsReader.getPropValue("company"), 
                ConfigsReader.getPropValue("EpicUserID"),
                ConfigsReader.getPropValue("EpicUserPWD")
        );
        
        // Select plant
        epiclogin.plant1();

        // Navigate to Training Venue Registration and fill form
        trainingVenueReg.submitTrainingVenueForm(testData);

        // Logout
        Logout.signOutPage();
    }

    /**
     * Test method for Training Venue Registration with complete data
     */
    @Test(priority = 3)
    public void trainingVenueRegistrationComplete() throws Throwable {

        if (isReportedRequired == true) {
            test = extent.createTest("Training Venue Registration - Complete Data")
                    .assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
                    .assignCategory("Training Venue Registration");
        }

        // Create complete test data
        HashMap<String, String> testData = new HashMap<>();
        testData.put("trainingVenueName", "Training Center B");
        testData.put("roomNumber", "Room-102");
        testData.put("capacity", "25");
        testData.put("phoneNumber", "9876543211");
        testData.put("uniqueCode", "VENUE002");
        testData.put("roomType", "Training Room");
        testData.put("contactPerson", "Jane Smith");
        testData.put("address1", "123 Business Park");
        testData.put("city", "New York");
        testData.put("pinZip", "10001");
        testData.put("costPerHour", "100");
        testData.put("emailId", "<EMAIL>");
        testData.put("address2", "Building A, Floor 2");
        testData.put("state", "NY");
        testData.put("roomEquipment", "Projector, Whiteboard, Audio System");
        testData.put("additionalInfo", "Air conditioned room with parking facility");

        // Login to application
        epiclogin.loginToApplication(
                ConfigsReader.getPropValue("company"), 
                ConfigsReader.getPropValue("EpicUserID"),
                ConfigsReader.getPropValue("EpicUserPWD")
        );
        
        // Select plant
        epiclogin.plant1();

        // Navigate to Training Venue Registration and fill form
        trainingVenueReg.submitTrainingVenueForm(testData);

        // Logout
        Logout.signOutPage();
    }
}
