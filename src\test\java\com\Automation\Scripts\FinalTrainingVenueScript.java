package com.Automation.Scripts;

import org.openqa.selenium.By;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.openqa.selenium.support.ui.ExpectedConditions;
import java.time.Duration;
import java.util.List;

/**
 * FINAL COMPREHENSIVE TRAINING VENUE REGISTRATION SCRIPT
 * 
 * This script uses EVERY possible strategy to fill the Training Venue form:
 * 1. EPIQ Application ID patterns (TrainingVenue_FieldName)
 * 2. Simple field names (FieldName)
 * 3. Name attributes
 * 4. XPath with contains()
 * 5. Label-based locators
 * 6. Position-based locators
 * 7. JavaScript injection
 * 8. DOM inspection
 * 
 * Author: Automation Team
 * Date: 2025-01-09
 */
public class FinalTrainingVenueScript {
    
    private static WebDriver driver;
    private static WebDriverWait wait;
    private static JavascriptExecutor js;
    
    // Test data for the form
    private static final String[] FIELD_VALUES = {
        "Conference Hall A",           // Training Venue Name
        "Room-101",                   // Room Number/Name
        "50",                         // Capacity
        "9876543210",                 // Phone Number
        "123 Business Park",          // Address1
        "New York",                   // City
        "10001",                      // Pin/Zip
        "100",                        // Cost Per Hour
        "VENUE001",                   // Unique Code
        "Conference Room",            // Room Type
        "John Doe",                   // Contact Person
        "<EMAIL>",       // Email ID
        "Building A, Floor 2",        // Address2
        "NY"                          // State
    };
    
    private static final String[] TEXTAREA_VALUES = {
        "Projector, Whiteboard, Audio System",           // Room Equipment
        "Air conditioned room with parking facility"     // Additional Information
    };
    
    public static void main(String[] args) {
        try {
            System.out.println("========================================");
            System.out.println("FINAL TRAINING VENUE REGISTRATION SCRIPT");
            System.out.println("========================================");
            
            initializeDriver();
            navigateToApplication();
            
            // Try multiple strategies in order
            boolean success = false;
            
            System.out.println("\n🔄 Strategy 1: EPIQ Application Patterns...");
            success = fillUsingEPIQPatterns();
            
            if (!success) {
                System.out.println("\n🔄 Strategy 2: DOM Analysis...");
                success = fillUsingDOMAnalysis();
            }
            
            if (!success) {
                System.out.println("\n🔄 Strategy 3: JavaScript Injection...");
                success = fillUsingJavaScript();
            }
            
            if (!success) {
                System.out.println("\n🔄 Strategy 4: Brute Force...");
                success = fillUsingBruteForce();
            }
            
            if (success) {
                System.out.println("\n✅ Form filled successfully!");
                submitForm();
                System.out.println("✅ Form submitted successfully!");
            } else {
                System.out.println("\n❌ All strategies failed!");
            }
            
            // Wait to see results
            Thread.sleep(5000);
            
        } catch (Exception e) {
            System.out.println("❌ Error: " + e.getMessage());
            e.printStackTrace();
        } finally {
            System.out.println("\n========================================");
            System.out.println("SCRIPT EXECUTION COMPLETED");
            System.out.println("========================================");
            // Uncomment to close browser: driver.quit();
        }
    }
    
    private static void initializeDriver() {
        System.out.println("🚀 Initializing Chrome driver...");
        driver = new ChromeDriver();
        wait = new WebDriverWait(driver, Duration.ofSeconds(15));
        js = (JavascriptExecutor) driver;
        driver.manage().window().maximize();
    }
    
    private static void navigateToApplication() {
        System.out.println("🌐 Navigating to EPIQ application...");
        driver.get("http://cqmsconfig05/EPIQ/Pages/MainPage?TaskId=#");
        
        // Wait for page to load completely
        wait.until(ExpectedConditions.jsReturnsValue("return document.readyState === 'complete'"));
        
        try {
            Thread.sleep(3000); // Additional wait for dynamic content
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        System.out.println("✅ Page loaded successfully");
    }
    
    /**
     * Strategy 1: Use EPIQ application naming patterns
     */
    private static boolean fillUsingEPIQPatterns() {
        String[] fieldNames = {
            "VenueName", "RoomNumber", "Capacity", "PhoneNumber", "Address1", 
            "City", "PinZip", "CostPerHour", "UniqueCode", "RoomType", 
            "ContactPerson", "EmailID", "Address2", "State"
        };
        
        int filledCount = 0;
        
        for (int i = 0; i < fieldNames.length && i < FIELD_VALUES.length; i++) {
            String fieldName = fieldNames[i];
            String value = FIELD_VALUES[i];
            
            // Try multiple EPIQ patterns
            String[] patterns = {
                "TrainingVenue_" + fieldName,
                fieldName,
                fieldName.toLowerCase(),
                fieldName.toUpperCase()
            };
            
            boolean filled = false;
            for (String pattern : patterns) {
                if (fillFieldByLocator(By.id(pattern), value, "ID: " + pattern) ||
                    fillFieldByLocator(By.name(pattern), value, "Name: " + pattern)) {
                    filled = true;
                    filledCount++;
                    break;
                }
            }
            
            if (!filled) {
                // Try XPath patterns
                String xpath = "//input[contains(@id,'" + fieldName + "') or contains(@name,'" + fieldName + "')]";
                if (fillFieldByLocator(By.xpath(xpath), value, "XPath: " + fieldName)) {
                    filledCount++;
                }
            }
        }
        
        // Fill textareas
        String[] textareaNames = {"RoomEquipment", "AdditionalInfo"};
        for (int i = 0; i < textareaNames.length && i < TEXTAREA_VALUES.length; i++) {
            String xpath = "//textarea[contains(@id,'" + textareaNames[i] + "') or contains(@name,'" + textareaNames[i] + "')]";
            if (fillFieldByLocator(By.xpath(xpath), TEXTAREA_VALUES[i], "Textarea: " + textareaNames[i])) {
                filledCount++;
            }
        }
        
        System.out.println("📊 EPIQ Patterns: Filled " + filledCount + " fields");
        return filledCount >= 7; // Minimum required fields
    }
    
    /**
     * Strategy 2: Analyze DOM structure and fill intelligently
     */
    private static boolean fillUsingDOMAnalysis() {
        try {
            // Find all visible input fields
            List<WebElement> inputs = driver.findElements(By.xpath("//input[@type='text' or not(@type) or @type='']"));
            inputs.addAll(driver.findElements(By.xpath("//textarea")));
            
            // Filter for visible and enabled
            inputs.removeIf(element -> {
                try {
                    return !element.isDisplayed() || !element.isEnabled();
                } catch (Exception e) {
                    return true;
                }
            });
            
            System.out.println("🔍 Found " + inputs.size() + " visible input fields");
            
            if (inputs.size() < 7) {
                return false;
            }
            
            // Fill fields in order
            int maxFields = Math.min(inputs.size(), FIELD_VALUES.length);
            for (int i = 0; i < maxFields; i++) {
                fillElementDirectly(inputs.get(i), FIELD_VALUES[i], "Field " + (i + 1));
            }
            
            // Fill remaining textareas
            List<WebElement> textareas = driver.findElements(By.xpath("//textarea"));
            for (int i = 0; i < Math.min(textareas.size(), TEXTAREA_VALUES.length); i++) {
                if (textareas.get(i).isDisplayed() && textareas.get(i).isEnabled()) {
                    fillElementDirectly(textareas.get(i), TEXTAREA_VALUES[i], "Textarea " + (i + 1));
                }
            }
            
            System.out.println("📊 DOM Analysis: Filled " + maxFields + " fields");
            return true;
            
        } catch (Exception e) {
            System.out.println("❌ DOM Analysis failed: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Strategy 3: Use JavaScript to find and fill fields
     */
    private static boolean fillUsingJavaScript() {
        try {
            // JavaScript to find all input fields
            String findInputsScript = 
                "var inputs = document.querySelectorAll('input[type=\"text\"], input:not([type]), textarea');" +
                "var visibleInputs = [];" +
                "for(var i = 0; i < inputs.length; i++) {" +
                "  var style = window.getComputedStyle(inputs[i]);" +
                "  if(style.display !== 'none' && style.visibility !== 'hidden' && inputs[i].offsetParent !== null) {" +
                "    visibleInputs.push(inputs[i]);" +
                "  }" +
                "}" +
                "return visibleInputs.length;";
            
            Long inputCount = (Long) js.executeScript(findInputsScript);
            System.out.println("🔍 JavaScript found " + inputCount + " visible inputs");
            
            if (inputCount < 7) {
                return false;
            }
            
            // Fill fields using JavaScript
            String[] allValues = new String[FIELD_VALUES.length + TEXTAREA_VALUES.length];
            System.arraycopy(FIELD_VALUES, 0, allValues, 0, FIELD_VALUES.length);
            System.arraycopy(TEXTAREA_VALUES, 0, allValues, FIELD_VALUES.length, TEXTAREA_VALUES.length);
            
            for (int i = 0; i < Math.min(inputCount.intValue(), allValues.length); i++) {
                String fillScript = 
                    "var inputs = document.querySelectorAll('input[type=\"text\"], input:not([type]), textarea');" +
                    "var visibleInputs = [];" +
                    "for(var j = 0; j < inputs.length; j++) {" +
                    "  var style = window.getComputedStyle(inputs[j]);" +
                    "  if(style.display !== 'none' && style.visibility !== 'hidden' && inputs[j].offsetParent !== null) {" +
                    "    visibleInputs.push(inputs[j]);" +
                    "  }" +
                    "}" +
                    "if(visibleInputs[" + i + "]) {" +
                    "  visibleInputs[" + i + "].value = '" + allValues[i].replace("'", "\\'") + "';" +
                    "  visibleInputs[" + i + "].dispatchEvent(new Event('input', { bubbles: true }));" +
                    "  visibleInputs[" + i + "].dispatchEvent(new Event('change', { bubbles: true }));" +
                    "  return true;" +
                    "}" +
                    "return false;";
                
                Boolean filled = (Boolean) js.executeScript(fillScript);
                if (filled) {
                    System.out.println("✅ JS filled field " + (i + 1) + ": " + allValues[i]);
                }
                
                Thread.sleep(100);
            }
            
            System.out.println("📊 JavaScript: Filled fields successfully");
            return true;
            
        } catch (Exception e) {
            System.out.println("❌ JavaScript strategy failed: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Strategy 4: Brute force - try every possible locator
     */
    private static boolean fillUsingBruteForce() {
        try {
            System.out.println("🔨 Attempting brute force approach...");
            
            // Get ALL elements on the page
            List<WebElement> allElements = driver.findElements(By.xpath("//*"));
            List<WebElement> inputElements = new java.util.ArrayList<>();
            
            // Find anything that looks like an input
            for (WebElement element : allElements) {
                try {
                    String tagName = element.getTagName().toLowerCase();
                    String type = element.getAttribute("type");
                    
                    if ((tagName.equals("input") && (type == null || type.equals("text") || type.equals(""))) ||
                        tagName.equals("textarea")) {
                        
                        if (element.isDisplayed() && element.isEnabled()) {
                            inputElements.add(element);
                        }
                    }
                } catch (Exception e) {
                    continue;
                }
            }
            
            System.out.println("🔍 Brute force found " + inputElements.size() + " input elements");
            
            if (inputElements.size() < 5) {
                return false;
            }
            
            // Fill what we can
            String[] allValues = new String[FIELD_VALUES.length + TEXTAREA_VALUES.length];
            System.arraycopy(FIELD_VALUES, 0, allValues, 0, FIELD_VALUES.length);
            System.arraycopy(TEXTAREA_VALUES, 0, allValues, FIELD_VALUES.length, TEXTAREA_VALUES.length);
            
            for (int i = 0; i < Math.min(inputElements.size(), allValues.length); i++) {
                fillElementDirectly(inputElements.get(i), allValues[i], "Brute force field " + (i + 1));
            }
            
            System.out.println("📊 Brute Force: Attempted to fill " + Math.min(inputElements.size(), allValues.length) + " fields");
            return true;
            
        } catch (Exception e) {
            System.out.println("❌ Brute force failed: " + e.getMessage());
            return false;
        }
    }
    
    private static boolean fillFieldByLocator(By locator, String value, String strategy) {
        try {
            WebElement element = driver.findElement(locator);
            if (element.isDisplayed() && element.isEnabled()) {
                fillElementDirectly(element, value, strategy);
                return true;
            }
        } catch (Exception e) {
            // Element not found or not interactable
        }
        return false;
    }
    
    private static void fillElementDirectly(WebElement element, String value, String description) {
        try {
            // Scroll to element
            js.executeScript("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", element);
            Thread.sleep(200);
            
            // Clear and fill
            element.clear();
            element.sendKeys(value);
            
            // Trigger events
            js.executeScript("arguments[0].dispatchEvent(new Event('input', { bubbles: true }));", element);
            js.executeScript("arguments[0].dispatchEvent(new Event('change', { bubbles: true }));", element);
            
            System.out.println("✅ " + description + ": " + value);
            Thread.sleep(200);
            
        } catch (Exception e) {
            System.out.println("❌ Failed " + description + ": " + e.getMessage());
        }
    }
    
    private static void submitForm() {
        System.out.println("\n🚀 Attempting to submit form...");
        
        // All possible submit button locators
        String[] submitLocators = {
            "btnSubmit",                                    // Most common EPIQ pattern
            "Submit",
            "TrainingVenue_Submit",
            "//button[@id='btnSubmit']",
            "//input[@type='submit']",
            "//button[text()='Submit']",
            "//input[@value='Submit']",
            "//button[contains(@class,'submit')]",
            "//button[contains(text(),'Submit')]",
            "//form//button[last()]",
            "//div[contains(@class,'button')]//button"
        };
        
        for (String locator : submitLocators) {
            try {
                WebElement submitBtn = null;
                
                if (!locator.startsWith("//")) {
                    try {
                        submitBtn = driver.findElement(By.id(locator));
                    } catch (Exception e) {
                        continue;
                    }
                } else {
                    try {
                        submitBtn = driver.findElement(By.xpath(locator));
                    } catch (Exception e) {
                        continue;
                    }
                }
                
                if (submitBtn != null && submitBtn.isDisplayed() && submitBtn.isEnabled()) {
                    js.executeScript("arguments[0].scrollIntoView(true);", submitBtn);
                    Thread.sleep(500);
                    
                    try {
                        submitBtn.click();
                    } catch (Exception e) {
                        js.executeScript("arguments[0].click();", submitBtn);
                    }
                    
                    System.out.println("✅ Submit clicked using: " + locator);
                    return;
                }
            } catch (Exception e) {
                continue;
            }
        }
        
        // Last resort: JavaScript form submission
        try {
            js.executeScript("document.querySelector('form').submit();");
            System.out.println("✅ Form submitted using JavaScript");
        } catch (Exception e) {
            System.out.println("❌ Could not submit form with any method");
        }
    }
}
