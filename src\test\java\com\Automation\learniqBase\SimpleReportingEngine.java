package com.Automation.learniqBase;

import org.openqa.selenium.WebElement;
import com.Automation.Utils.SimpleExtentReporter;

/**
 * Simple Reporting Engine - Wrapper around existing action methods
 * Uses simple ExtentReports without affecting original click2/sendKeys2 methods
 */
public class SimpleReportingEngine extends OQActionEngine {
    
    private boolean useSimpleReporting = false;
    
    public SimpleReportingEngine() {
        super();
    }
    
    public SimpleReportingEngine(String url) {
        super(url);
    }
    
    /**
     * Enable simple reporting mode
     */
    public void enableSimpleReporting() {
        this.useSimpleReporting = true;
    }
    
    /**
     * Disable simple reporting mode (use original reporting)
     */
    public void disableSimpleReporting() {
        this.useSimpleReporting = false;
    }
    
    /**
     * Simple click - logs only basic action without detailed tables
     */
    public void simpleClick(WebElement element, String elementName) {
        try {
            if (useSimpleReporting) {
                // Use simple reporting
                element.click();
                SimpleExtentReporter.logStep("Clicked on " + elementName);
            } else {
                // Use original detailed reporting
                click2(element, 
                    "Click on " + elementName,
                    elementName + " should be clicked",
                    elementName + " is clicked",
                    elementName + "_click");
            }
        } catch (Exception e) {
            if (useSimpleReporting) {
                SimpleExtentReporter.logFailedStep("Click on " + elementName, e.getMessage());
            }
            throw e;
        }
    }
    
    /**
     * Simple sendKeys - logs only basic action without detailed tables
     */
    public void simpleSendKeys(WebElement element, String text, String fieldName) {
        try {
            if (useSimpleReporting) {
                // Use simple reporting
                element.clear();
                element.sendKeys(text);
                SimpleExtentReporter.logStep("Entered '" + text + "' in " + fieldName);
            } else {
                // Use original detailed reporting
                sendKeys2(element,
                    "Enter text in " + fieldName,
                    text,
                    "Text should be entered in " + fieldName,
                    "Text is entered in " + fieldName,
                    fieldName + "_input");
            }
        } catch (Exception e) {
            if (useSimpleReporting) {
                SimpleExtentReporter.logFailedStep("Enter text in " + fieldName, e.getMessage());
            }
            throw e;
        }
    }
    
    /**
     * Auto-detect element name and click
     */
    public void autoClick(WebElement element) {
        String elementName = getElementName(element);
        simpleClick(element, elementName);
    }
    
    /**
     * Auto-detect field name and send keys
     */
    public void autoSendKeys(WebElement element, String text) {
        String fieldName = getElementName(element);
        simpleSendKeys(element, text, fieldName);
    }
    
    /**
     * Simple verification without detailed reporting
     */
    public void simpleVerify(WebElement element, String expectedText, String elementName) {
        try {
            String actualText = element.getText();
            boolean passed = actualText.contains(expectedText);
            
            if (useSimpleReporting) {
                if (passed) {
                    SimpleExtentReporter.logStep("Verified " + elementName + " contains '" + expectedText + "'");
                } else {
                    SimpleExtentReporter.logFailedStep("Verify " + elementName, 
                        "Expected: '" + expectedText + "', Actual: '" + actualText + "'");
                    throw new AssertionError("Verification failed");
                }
            } else {
                // Use original verification method if available
                if (passed) {
                    SimpleExtentReporter.logStep("Verification passed for " + elementName);
                } else {
                    throw new AssertionError("Verification failed for " + elementName);
                }
            }
        } catch (Exception e) {
            if (useSimpleReporting) {
                SimpleExtentReporter.logFailedStep("Verify " + elementName, e.getMessage());
            }
            throw e;
        }
    }
    
    /**
     * Navigate with simple logging
     */
    public void simpleNavigate(String url) {
        try {
            driver.get(url);
            if (useSimpleReporting) {
                SimpleExtentReporter.logStep("Navigated to: " + url);
            }
        } catch (Exception e) {
            if (useSimpleReporting) {
                SimpleExtentReporter.logFailedStep("Navigate to " + url, e.getMessage());
            }
            throw e;
        }
    }
    
    /**
     * Log custom action
     */
    public void logAction(String action) {
        if (useSimpleReporting) {
            SimpleExtentReporter.logStep(action);
        }
    }
    
    /**
     * Log test info
     */
    public void logInfo(String message) {
        if (useSimpleReporting) {
            SimpleExtentReporter.logInfo(message);
        }
    }
    
    /**
     * Mark test as passed
     */
    public void markTestPassed() {
        if (useSimpleReporting) {
            SimpleExtentReporter.markTestPassed();
        }
    }
    
    /**
     * Mark test as failed
     */
    public void markTestFailed(String reason) {
        if (useSimpleReporting) {
            SimpleExtentReporter.markTestFailed(reason);
        }
    }
    
    /**
     * Extract element name from various attributes
     */
    private String getElementName(WebElement element) {
        try {
            String name = element.getAttribute("data-testid");
            if (name != null && !name.isEmpty()) return name;
            
            name = element.getAttribute("id");
            if (name != null && !name.isEmpty()) return name;
            
            name = element.getAttribute("name");
            if (name != null && !name.isEmpty()) return name;
            
            name = element.getAttribute("placeholder");
            if (name != null && !name.isEmpty()) return name;
            
            name = element.getText();
            if (name != null && !name.isEmpty() && name.length() < 30) return name;
            
            return element.getTagName();
        } catch (Exception e) {
            return "Unknown Element";
        }
    }
    
    /**
     * Override original click2 to optionally use simple reporting
     */
    @Override
    public void click2(WebElement element, String stepDescription, String acceptanceCriteria, 
                      String actualResult, String scName) {
        if (useSimpleReporting) {
            // Extract element name from step description or use scName
            String elementName = scName.replace("_", " ");
            simpleClick(element, elementName);
        } else {
            // Use original method
            super.click2(element, stepDescription, acceptanceCriteria, actualResult, scName);
        }
    }
    
    /**
     * Override original sendKeys2 to optionally use simple reporting
     */
    @Override
    public void sendKeys2(WebElement element, String stepDescription, String text, 
                         String acceptanceCriteria, String actualResult, String scName) {
        if (useSimpleReporting) {
            // Extract field name from step description or use scName
            String fieldName = scName.replace("_", " ");
            simpleSendKeys(element, text, fieldName);
        } else {
            // Use original method
            super.sendKeys2(element, stepDescription, text, acceptanceCriteria, actualResult, scName);
        }
    }
}
