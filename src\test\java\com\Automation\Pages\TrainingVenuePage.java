package com.Automation.Pages;

import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;

/**
 * Training Venue Registration Page Object
 * Uses simple name/id locators
 */
public class TrainingVenuePage {
    
    private WebDriver driver;
    
    // Page Elements using name/id locators
    @FindBy(name = "VenueName")
    private WebElement venueNameField;
    
    @FindBy(id = "VenueName")
    private WebElement venueNameFieldById;
    
    @FindBy(name = "RoomNumber")
    private WebElement roomNumberField;
    
    @FindBy(id = "RoomNumber")
    private WebElement roomNumberFieldById;
    
    @FindBy(name = "Capacity")
    private WebElement capacityField;
    
    @FindBy(id = "Capacity")
    private WebElement capacityFieldById;
    
    @FindBy(name = "PhoneNumber")
    private WebElement phoneNumberField;
    
    @FindBy(id = "PhoneNumber")
    private WebElement phoneNumberFieldById;
    
    @FindBy(name = "Address1")
    private WebElement address1Field;
    
    @FindBy(id = "Address1")
    private WebElement address1FieldById;
    
    @FindBy(name = "City")
    private WebElement cityField;
    
    @FindBy(id = "City")
    private WebElement cityFieldById;
    
    @FindBy(name = "PinZip")
    private WebElement pinZipField;
    
    @FindBy(id = "PinZip")
    private WebElement pinZipFieldById;
    
    @FindBy(name = "CostPerHour")
    private WebElement costPerHourField;
    
    @FindBy(id = "CostPerHour")
    private WebElement costPerHourFieldById;
    
    @FindBy(name = "UniqueCode")
    private WebElement uniqueCodeField;
    
    @FindBy(id = "UniqueCode")
    private WebElement uniqueCodeFieldById;
    
    @FindBy(name = "RoomType")
    private WebElement roomTypeField;
    
    @FindBy(id = "RoomType")
    private WebElement roomTypeFieldById;
    
    @FindBy(name = "ContactPerson")
    private WebElement contactPersonField;
    
    @FindBy(id = "ContactPerson")
    private WebElement contactPersonFieldById;
    
    @FindBy(name = "EmailID")
    private WebElement emailIdField;
    
    @FindBy(id = "EmailID")
    private WebElement emailIdFieldById;
    
    @FindBy(name = "Address2")
    private WebElement address2Field;
    
    @FindBy(id = "Address2")
    private WebElement address2FieldById;
    
    @FindBy(name = "State")
    private WebElement stateField;
    
    @FindBy(id = "State")
    private WebElement stateFieldById;
    
    @FindBy(name = "RoomEquipment")
    private WebElement roomEquipmentField;
    
    @FindBy(id = "RoomEquipment")
    private WebElement roomEquipmentFieldById;
    
    @FindBy(name = "AdditionalInfo")
    private WebElement additionalInfoField;
    
    @FindBy(id = "AdditionalInfo")
    private WebElement additionalInfoFieldById;
    
    // Submit button
    @FindBy(id = "btnSubmit")
    private WebElement submitButton;
    
    @FindBy(name = "Submit")
    private WebElement submitButtonByName;
    
    // Constructor
    public TrainingVenuePage(WebDriver driver) {
        this.driver = driver;
        PageFactory.initElements(driver, this);
    }
    
    // Page Actions
    public void enterVenueName(String venueName) {
        fillField(venueNameField, venueNameFieldById, venueName, "Venue Name");
    }
    
    public void enterRoomNumber(String roomNumber) {
        fillField(roomNumberField, roomNumberFieldById, roomNumber, "Room Number");
    }
    
    public void enterCapacity(String capacity) {
        fillField(capacityField, capacityFieldById, capacity, "Capacity");
    }
    
    public void enterPhoneNumber(String phoneNumber) {
        fillField(phoneNumberField, phoneNumberFieldById, phoneNumber, "Phone Number");
    }
    
    public void enterAddress1(String address1) {
        fillField(address1Field, address1FieldById, address1, "Address1");
    }
    
    public void enterCity(String city) {
        fillField(cityField, cityFieldById, city, "City");
    }
    
    public void enterPinZip(String pinZip) {
        fillField(pinZipField, pinZipFieldById, pinZip, "Pin/Zip");
    }
    
    public void enterCostPerHour(String costPerHour) {
        fillField(costPerHourField, costPerHourFieldById, costPerHour, "Cost Per Hour");
    }
    
    public void enterUniqueCode(String uniqueCode) {
        fillField(uniqueCodeField, uniqueCodeFieldById, uniqueCode, "Unique Code");
    }
    
    public void enterRoomType(String roomType) {
        fillField(roomTypeField, roomTypeFieldById, roomType, "Room Type");
    }
    
    public void enterContactPerson(String contactPerson) {
        fillField(contactPersonField, contactPersonFieldById, contactPerson, "Contact Person");
    }
    
    public void enterEmailId(String emailId) {
        fillField(emailIdField, emailIdFieldById, emailId, "Email ID");
    }
    
    public void enterAddress2(String address2) {
        fillField(address2Field, address2FieldById, address2, "Address2");
    }
    
    public void enterState(String state) {
        fillField(stateField, stateFieldById, state, "State");
    }
    
    public void enterRoomEquipment(String roomEquipment) {
        fillField(roomEquipmentField, roomEquipmentFieldById, roomEquipment, "Room Equipment");
    }
    
    public void enterAdditionalInfo(String additionalInfo) {
        fillField(additionalInfoField, additionalInfoFieldById, additionalInfo, "Additional Info");
    }
    
    public void clickSubmit() {
        try {
            submitButton.click();
            System.out.println("✓ Submit button clicked (by ID)");
        } catch (Exception e) {
            try {
                submitButtonByName.click();
                System.out.println("✓ Submit button clicked (by Name)");
            } catch (Exception e2) {
                System.out.println("✗ Failed to click submit button");
            }
        }
    }
    
    // Complete form filling method
    public void fillCompleteForm(String venueName, String roomNumber, String capacity, 
                                String phoneNumber, String uniqueCode, String roomType, 
                                String contactPerson) {
        enterVenueName(venueName);
        enterRoomNumber(roomNumber);
        enterCapacity(capacity);
        enterPhoneNumber(phoneNumber);
        enterUniqueCode(uniqueCode);
        enterRoomType(roomType);
        enterContactPerson(contactPerson);
    }
    
    public void fillOptionalFields(String address1, String city, String pinZip, 
                                  String costPerHour, String emailId, String address2, 
                                  String state, String roomEquipment, String additionalInfo) {
        if (address1 != null) enterAddress1(address1);
        if (city != null) enterCity(city);
        if (pinZip != null) enterPinZip(pinZip);
        if (costPerHour != null) enterCostPerHour(costPerHour);
        if (emailId != null) enterEmailId(emailId);
        if (address2 != null) enterAddress2(address2);
        if (state != null) enterState(state);
        if (roomEquipment != null) enterRoomEquipment(roomEquipment);
        if (additionalInfo != null) enterAdditionalInfo(additionalInfo);
    }
    
    public void submitForm() {
        clickSubmit();
    }
    
    // Helper method to fill field with fallback
    private void fillField(WebElement nameElement, WebElement idElement, String value, String fieldName) {
        try {
            nameElement.clear();
            nameElement.sendKeys(value);
            System.out.println("✓ " + fieldName + " (by name): " + value);
            Thread.sleep(200);
        } catch (Exception e) {
            try {
                idElement.clear();
                idElement.sendKeys(value);
                System.out.println("✓ " + fieldName + " (by id): " + value);
                Thread.sleep(200);
            } catch (Exception e2) {
                System.out.println("✗ Failed to fill " + fieldName);
            }
        }
    }
}
