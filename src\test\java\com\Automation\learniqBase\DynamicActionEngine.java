package com.Automation.learniqBase;

import org.openqa.selenium.WebElement;
import com.Automation.Utils.DynamicReportContent;
import com.Automation.Utils.DynamicReportContent.ReportData;
import com.aventstack.extentreports.markuputils.MarkupHelper;
import com.aventstack.extentreports.markuputils.Markup;
import com.aventstack.extentreports.Status;

/**
 * Dynamic Action Engine with Auto-Generated Report Content
 * Extends OQActionEngine to provide dynamic reporting capabilities
 */
public class DynamicActionEngine extends OQActionEngine {
    
    public DynamicActionEngine() {
        super();
    }
    
    public DynamicActionEngine(String url) {
        super(url);
    }
    
    /**
     * Dynamic click method - automatically generates report content
     */
    public void dynamicClick(WebElement element) {
        ReportData reportData = DynamicReportContent.generateReportData("click", element);
        click2(element, reportData.stepDescription, reportData.acceptanceCriteria, 
               reportData.actualResult, reportData.screenshotName);
    }
    
    /**
     * Dynamic click with custom step description
     */
    public void dynamicClick(WebElement element, String customStepDescription) {
        ReportData reportData = DynamicReportContent.generateReportData("click", element);
        click2(element, customStepDescription, reportData.acceptanceCriteria, 
               reportData.actualResult, reportData.screenshotName);
    }
    
    /**
     * Dynamic sendKeys method - automatically generates report content
     */
    public void dynamicSendKeys(WebElement element, String text) {
        ReportData reportData = DynamicReportContent.generateReportData("sendkeys", element, text);
        sendKeys2(element, reportData.stepDescription, text, reportData.acceptanceCriteria, 
                  reportData.actualResult, reportData.screenshotName);
    }
    
    /**
     * Smart click - analyzes element and generates contextual content
     */
    public void smartClick(WebElement element) {
        try {
            if (isReportedRequired) {
                n = n + 1;
                highlightEle2(element);
                
                // Generate dynamic content based on element analysis
                String elementName = getSmartElementName(element);
                String elementType = getSmartElementType(element);
                String expectedAction = getExpectedAction(element);
                
                String stepDescription = String.format("Click on '%s' %s", elementName, elementType);
                String acceptanceCriteria = String.format("'%s' should be clicked and %s", elementName, expectedAction);
                String actualResult = String.format("'%s' is getting clicked and %s", elementName, expectedAction.replace("should", "is"));
                
                // Create dynamic table
                String[][] data = {
                    {"<b>Step No</b>", "<b>Step Description</b>", "<b>Acceptance Criteria</b>", "<b>Actual Result</b>"},
                    {String.valueOf(n), stepDescription, "<div><b>* </b>" + acceptanceCriteria, "<div><b>* </b>" + actualResult}
                };
                
                Markup tableMarkup = MarkupHelper.createTable(data);
                String modifiedMarkup = modifyColumnWidth(tableMarkup.getMarkup());
                test.log(Status.PASS, modifiedMarkup);
                
                // Perform click
                waitForElementVisibile(element);
                element.click();
                isAlertPresent(driver);
                
                // Attach screenshot with dynamic name
                String screenshotName = elementName.replaceAll("[^a-zA-Z0-9]", "_") + "_clicked";
                attachScreenshot(driver, true, screenshotName, "click2");
            } else {
                waitForElementVisibile(element);
                element.click();
                isAlertPresent(driver);
            }
        } catch (Exception e) {
            if (isReportedRequired) {
                test.log(Status.FAIL, "<b>Step No. " + n + "</b> Failed to click element");
                test.log(Status.FAIL, "Exception: " + e.getMessage());
                attachScreenshot(driver, true, "click_failed", "click2");
            }
        }
    }
    
    /**
     * Smart sendKeys - analyzes input field and generates contextual content
     */
    public void smartSendKeys(WebElement element, String text) {
        try {
            if (isReportedRequired) {
                n = n + 1;
                waitForElementVisibile(element);
                element.clear();
                highlightEle2(element);
                
                // Generate dynamic content based on field analysis
                String fieldName = getSmartElementName(element);
                String fieldType = getFieldType(element);
                String dataType = analyzeInputData(text);
                
                String stepDescription = String.format("Enter %s '%s' in '%s' %s", dataType, text, fieldName, fieldType);
                String acceptanceCriteria = String.format("Entered %s should be displayed in '%s' %s", dataType, fieldName, fieldType);
                String actualResult = String.format("Entered %s is getting displayed in '%s' %s", dataType, fieldName, fieldType);
                
                // Create dynamic table
                String[][] data = {
                    {"<b>Step No</b>", "<b>Step Description</b>", "<b>Acceptance Criteria</b>", "<b>Actual Result</b>"},
                    {String.valueOf(n), stepDescription, "<div><b>* </b>" + acceptanceCriteria, "<div><b>* </b>" + actualResult}
                };
                
                Markup tableMarkup = MarkupHelper.createTable(data);
                String modifiedMarkup = modifyColumnWidth(tableMarkup.getMarkup());
                test.log(Status.PASS, modifiedMarkup);
                
                // Perform sendKeys
                element.sendKeys(text);
                
                // Attach screenshot with dynamic name
                String screenshotName = fieldName.replaceAll("[^a-zA-Z0-9]", "_") + "_input";
                attachScreenshot(driver, true, screenshotName, "sendKeys");
                
                jsExecutor.executeScript("arguments[0].style.border=''", element);
            } else {
                waitForElementVisibile(element);
                element.clear();
                element.sendKeys(text);
            }
        } catch (Exception e) {
            if (isReportedRequired) {
                test.log(Status.FAIL, "<b>Step No. " + n + "</b> Failed to enter data in field");
                test.log(Status.FAIL, "Exception: " + e.getMessage());
                attachScreenshot(driver, true, "sendKeys_failed", "sendKeys");
            }
        }
    }
    
    /**
     * Get smart element name by analyzing multiple attributes
     */
    private String getSmartElementName(WebElement element) {
        try {
            // Priority order for getting element name
            String[] attributes = {"data-testid", "aria-label", "title", "placeholder", "name", "id", "alt"};
            
            for (String attr : attributes) {
                String value = element.getAttribute(attr);
                if (value != null && !value.trim().isEmpty()) {
                    return cleanElementName(value);
                }
            }
            
            // Try text content
            String text = element.getText();
            if (text != null && !text.trim().isEmpty() && text.length() < 50) {
                return cleanElementName(text);
            }
            
            // Fallback to tag name
            return element.getTagName();
            
        } catch (Exception e) {
            return "Unknown Element";
        }
    }
    
    /**
     * Get smart element type
     */
    private String getSmartElementType(WebElement element) {
        try {
            String tagName = element.getTagName().toLowerCase();
            String type = element.getAttribute("type");
            
            if ("input".equals(tagName)) {
                if (type != null) {
                    switch (type.toLowerCase()) {
                        case "button": case "submit": return "button";
                        case "text": case "email": case "password": return "field";
                        case "checkbox": return "checkbox";
                        case "radio": return "radio button";
                        default: return "input field";
                    }
                }
                return "input field";
            } else if ("button".equals(tagName)) {
                return "button";
            } else if ("select".equals(tagName)) {
                return "dropdown";
            } else if ("textarea".equals(tagName)) {
                return "text area";
            } else if ("a".equals(tagName)) {
                return "link";
            }
            
            return "element";
        } catch (Exception e) {
            return "element";
        }
    }
    
    /**
     * Analyze expected action based on element properties
     */
    private String getExpectedAction(WebElement element) {
        try {
            String tagName = element.getTagName().toLowerCase();
            String type = element.getAttribute("type");
            String className = element.getAttribute("class");
            
            if ("button".equals(tagName) || "submit".equals(type)) {
                if (className != null && className.contains("submit")) {
                    return "form should be submitted";
                }
                return "corresponding action should be performed";
            } else if ("a".equals(tagName)) {
                return "navigation should occur";
            } else if ("select".equals(tagName)) {
                return "dropdown should be opened";
            }
            
            return "next screen should be displayed";
        } catch (Exception e) {
            return "action should be performed";
        }
    }
    
    /**
     * Get field type for input elements
     */
    private String getFieldType(WebElement element) {
        try {
            String type = element.getAttribute("type");
            if (type != null) {
                switch (type.toLowerCase()) {
                    case "email": return "email field";
                    case "password": return "password field";
                    case "number": return "number field";
                    case "date": return "date field";
                    case "tel": return "phone field";
                    default: return "field";
                }
            }
            return "field";
        } catch (Exception e) {
            return "field";
        }
    }
    
    /**
     * Analyze input data type
     */
    private String analyzeInputData(String text) {
        if (text == null || text.isEmpty()) return "data";
        
        if (text.matches("\\d+")) return "numeric data";
        if (text.matches("[a-zA-Z\\s]+")) return "text data";
        if (text.contains("@")) return "email";
        if (text.matches("\\d{4}-\\d{2}-\\d{2}")) return "date";
        if (text.matches("\\d{10}")) return "phone number";
        
        return "data";
    }
    
    /**
     * Clean element name for better readability
     */
    private String cleanElementName(String name) {
        return name.replaceAll("([a-z])([A-Z])", "$1 $2")  // camelCase to words
                  .replaceAll("_", " ")                      // underscores to spaces
                  .replaceAll("-", " ")                      // hyphens to spaces
                  .replaceAll("\\s+", " ")                   // multiple spaces to single
                  .trim();
    }
}
