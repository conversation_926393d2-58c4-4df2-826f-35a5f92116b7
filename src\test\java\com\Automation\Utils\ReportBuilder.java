package com.Automation.Utils;

import org.openqa.selenium.WebElement;
import java.util.ArrayList;
import java.util.List;

/**
 * Fluent API for Building Dynamic Report Content
 */
public class ReportBuilder {
    
    private String stepDescription;
    private String acceptanceCriteria;
    private String actualResult;
    private String screenshotName;
    private String businessContext;
    private String userStory;
    private List<String> tags = new ArrayList<>();
    
    public static ReportBuilder forAction(String action) {
        return new ReportBuilder().withAction(action);
    }
    
    public static ReportBuilder forElement(WebElement element) {
        return new ReportBuilder().withElement(element);
    }
    
    public ReportBuilder withAction(String action) {
        this.stepDescription = action;
        return this;
    }
    
    public ReportBuilder withElement(WebElement element) {
        String elementName = extractElementName(element);
        this.stepDescription = (this.stepDescription != null) 
            ? this.stepDescription + " on '" + elementName + "'"
            : "Interact with '" + elementName + "'";
        return this;
    }
    
    public ReportBuilder withElementName(String elementName) {
        this.stepDescription = (this.stepDescription != null) 
            ? this.stepDescription.replace("element", "'" + elementName + "'")
            : "Interact with '" + elementName + "'";
        return this;
    }
    
    public ReportBuilder expecting(String expectedResult) {
        this.acceptanceCriteria = expectedResult;
        return this;
    }
    
    public ReportBuilder shouldResult(String result) {
        this.acceptanceCriteria = result;
        return this;
    }
    
    public ReportBuilder actuallyResults(String result) {
        this.actualResult = result;
        return this;
    }
    
    public ReportBuilder withScreenshot(String name) {
        this.screenshotName = name;
        return this;
    }
    
    public ReportBuilder inBusinessContext(String context) {
        this.businessContext = context;
        return this;
    }
    
    public ReportBuilder forUserStory(String story) {
        this.userStory = story;
        return this;
    }
    
    public ReportBuilder withTag(String tag) {
        this.tags.add(tag);
        return this;
    }
    
    public ReportBuilder withTags(String... tags) {
        for (String tag : tags) {
            this.tags.add(tag);
        }
        return this;
    }
    
    // Predefined action builders
    public static ReportBuilder clickOn(WebElement element) {
        return new ReportBuilder()
            .withAction("Click")
            .withElement(element)
            .expecting("Element should be clicked successfully")
            .actuallyResults("Element is getting clicked successfully");
    }
    
    public static ReportBuilder enterText(String text) {
        return new ReportBuilder()
            .withAction("Enter '" + text + "'")
            .expecting("Text should be entered successfully")
            .actuallyResults("Text is getting entered successfully");
    }
    
    public static ReportBuilder selectFrom(WebElement dropdown, String option) {
        return new ReportBuilder()
            .withAction("Select '" + option + "'")
            .withElement(dropdown)
            .expecting("Option should be selected from dropdown")
            .actuallyResults("Option is getting selected from dropdown");
    }
    
    public static ReportBuilder verify(String condition) {
        return new ReportBuilder()
            .withAction("Verify " + condition)
            .expecting(condition + " should be true")
            .actuallyResults(condition + " is verified as true");
    }
    
    public static ReportBuilder navigateTo(String page) {
        return new ReportBuilder()
            .withAction("Navigate to " + page)
            .expecting("Should navigate to " + page + " successfully")
            .actuallyResults("Navigation to " + page + " is successful");
    }
    
    // Business context builders
    public static ReportBuilder loginAs(String userType) {
        return new ReportBuilder()
            .withAction("Login as " + userType)
            .expecting("User should be authenticated successfully")
            .actuallyResults("User is getting authenticated successfully")
            .inBusinessContext("User Authentication")
            .forUserStory("As a " + userType + ", I want to access the application")
            .withTag("authentication", "login");
    }
    
    public static ReportBuilder submitForm(String formName) {
        return new ReportBuilder()
            .withAction("Submit " + formName + " form")
            .expecting("Form should be submitted and processed successfully")
            .actuallyResults("Form is getting submitted and processed successfully")
            .inBusinessContext("Data Submission")
            .withTag("form", "submission");
    }
    
    public static ReportBuilder searchFor(String searchTerm) {
        return new ReportBuilder()
            .withAction("Search for '" + searchTerm + "'")
            .expecting("Search results should be displayed for '" + searchTerm + "'")
            .actuallyResults("Search results are getting displayed for '" + searchTerm + "'")
            .inBusinessContext("Information Retrieval")
            .withTag("search", "query");
    }
    
    // Build final report data
    public ReportData build() {
        // Auto-generate missing fields
        if (acceptanceCriteria == null) {
            acceptanceCriteria = "Action should be completed successfully";
        }
        if (actualResult == null) {
            actualResult = acceptanceCriteria.replace("should", "is getting");
        }
        if (screenshotName == null) {
            screenshotName = stepDescription.replaceAll("[^a-zA-Z0-9]", "_").toLowerCase();
        }
        
        return new ReportData(stepDescription, acceptanceCriteria, actualResult, screenshotName, 
                             businessContext, userStory, tags);
    }
    
    private String extractElementName(WebElement element) {
        try {
            String name = element.getAttribute("data-testid");
            if (name != null && !name.isEmpty()) return name;
            
            name = element.getAttribute("id");
            if (name != null && !name.isEmpty()) return name;
            
            name = element.getAttribute("name");
            if (name != null && !name.isEmpty()) return name;
            
            name = element.getText();
            if (name != null && !name.isEmpty() && name.length() < 30) return name;
            
            return element.getTagName();
        } catch (Exception e) {
            return "element";
        }
    }
    
    // Enhanced ReportData class
    public static class ReportData {
        public final String stepDescription;
        public final String acceptanceCriteria;
        public final String actualResult;
        public final String screenshotName;
        public final String businessContext;
        public final String userStory;
        public final List<String> tags;
        
        public ReportData(String stepDescription, String acceptanceCriteria, String actualResult, 
                         String screenshotName, String businessContext, String userStory, List<String> tags) {
            this.stepDescription = stepDescription;
            this.acceptanceCriteria = acceptanceCriteria;
            this.actualResult = actualResult;
            this.screenshotName = screenshotName;
            this.businessContext = businessContext;
            this.userStory = userStory;
            this.tags = new ArrayList<>(tags);
        }
        
        public String getFormattedTags() {
            return tags.isEmpty() ? "" : "[" + String.join(", ", tags) + "]";
        }
        
        public String getEnhancedStepDescription() {
            StringBuilder sb = new StringBuilder(stepDescription);
            if (businessContext != null) {
                sb.append(" (").append(businessContext).append(")");
            }
            if (!tags.isEmpty()) {
                sb.append(" ").append(getFormattedTags());
            }
            return sb.toString();
        }
    }
}
