package com.Automation.Tests;

import org.openqa.selenium.WebDriver;
import org.openqa.selenium.chrome.ChromeDriver;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import com.Automation.Pages.TrainingVenuePage;

/**
 * Training Venue Test Class using Page Object Model
 */
public class TrainingVenueTest {
    
    private WebDriver driver;
    private TrainingVenuePage trainingVenuePage;
    
    @BeforeMethod
    public void setUp() {
        driver = new ChromeDriver();
        driver.manage().window().maximize();
        driver.get("http://cqmsconfig05/EPIQ/Pages/MainPage?TaskId=#");
        
        // Wait for page to load
        try {
            Thread.sleep(3000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        
        // Initialize page object
        trainingVenuePage = new TrainingVenuePage(driver);
    }
    
    @Test(priority = 1)
    public void testTrainingVenueRegistrationMandatoryFields() {
        System.out.println("=== Test: Mandatory Fields Only ===");
        
        // Fill mandatory fields
        trainingVenuePage.fillCompleteForm(
            "Conference Hall A",     // Venue Name
            "Room-101",             // Room Number
            "50",                   // Capacity
            "9876543210",           // Phone Number
            "VENUE001",             // Unique Code
            "Conference Room",      // Room Type
            "John Doe"              // Contact Person
        );
        
        // Submit form
        trainingVenuePage.submitForm();
        
        System.out.println("=== Mandatory Fields Test Completed ===");
    }
    
    @Test(priority = 2)
    public void testTrainingVenueRegistrationAllFields() {
        System.out.println("=== Test: All Fields ===");
        
        // Fill mandatory fields
        trainingVenuePage.fillCompleteForm(
            "Training Center B",     // Venue Name
            "Room-102",             // Room Number
            "25",                   // Capacity
            "9876543211",           // Phone Number
            "VENUE002",             // Unique Code
            "Training Room",        // Room Type
            "Jane Smith"            // Contact Person
        );
        
        // Fill optional fields
        trainingVenuePage.fillOptionalFields(
            "123 Business Park",                              // Address1
            "New York",                                       // City
            "10001",                                         // Pin/Zip
            "100",                                           // Cost Per Hour
            "<EMAIL>",                        // Email ID
            "Building A, Floor 2",                           // Address2
            "NY",                                            // State
            "Projector, Whiteboard, Audio System",          // Room Equipment
            "Air conditioned room with parking facility"     // Additional Info
        );
        
        // Submit form
        trainingVenuePage.submitForm();
        
        System.out.println("=== All Fields Test Completed ===");
    }
    
    @Test(priority = 3)
    public void testTrainingVenueRegistrationStepByStep() {
        System.out.println("=== Test: Step by Step ===");
        
        // Fill fields one by one
        trainingVenuePage.enterVenueName("Executive Conference Room");
        trainingVenuePage.enterRoomNumber("Room-103");
        trainingVenuePage.enterCapacity("75");
        trainingVenuePage.enterPhoneNumber("9876543212");
        trainingVenuePage.enterAddress1("456 Corporate Plaza");
        trainingVenuePage.enterCity("Boston");
        trainingVenuePage.enterPinZip("02101");
        trainingVenuePage.enterCostPerHour("150");
        trainingVenuePage.enterUniqueCode("VENUE003");
        trainingVenuePage.enterRoomType("Executive Room");
        trainingVenuePage.enterContactPerson("Mike Johnson");
        trainingVenuePage.enterEmailId("<EMAIL>");
        trainingVenuePage.enterAddress2("Tower B, 5th Floor");
        trainingVenuePage.enterState("MA");
        trainingVenuePage.enterRoomEquipment("Smart Board, Video Conferencing, Sound System");
        trainingVenuePage.enterAdditionalInfo("Premium room with catering facility and valet parking");
        
        // Submit form
        trainingVenuePage.submitForm();
        
        System.out.println("=== Step by Step Test Completed ===");
    }
    
    @AfterMethod
    public void tearDown() {
        // Wait to see results
        try {
            Thread.sleep(5000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        
        // Close browser (uncomment to close)
        // driver.quit();
    }
}
