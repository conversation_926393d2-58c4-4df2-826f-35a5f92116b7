#URLs
#applicationUrl=https://cqmprdtstapp01.aurelius.com:561/epiqByCaliber_310V_SP_TST/
#applicationUrl=https://cqmprdtstapp01.aurelius.com:561/epiqByCaliber_310HF_TST/
applicationUrl=http://cqmsconfig05/EPIQ/

applicationUrl310=https://cqmprdtstapp01.aurelius.com:561/epiqByCaliber_310V_SP_TST
applicationUrl310Slash=https://cqmprdtstapp01.aurelius.com:561/epiqByCaliber_310V_SP_TST/Pages/PlantSelection?ref=login
applicationUrl320=https://cqmprdtstapp01.aurelius.com:561/epiqbyCaiber320V_TST
#SSOUrl=https://cqmprdtst1.aurelius.com:561/PL009200_SSO_GM_MGR_TST/Login
#SSOUrl=https://cqmsqasrv05.aurelius.com/CaliberSSO_MGR_310/
SSOUrl=http://cqmsconfig05/SSO/Login?UserCulture=en-US
210SSOUrl=https://cqmprdtst1.aurelius.com:561/CaliberSSO_APP_GM_TST
MultisessionSSOURL320=https://cqmprdtst1.aurelius.com:561/CaliberSSO_APP_GM_TST/MainPage
URL=https://cqmprdtst1.aurelius.com:561/learnIQ_TST
#MultisessionURL=https://cqmprdtst1.aurelius.com:561/epiqByCaliber_TST/Pages/PlantSelection?ref=login

MultisessionURL=http://cqmsconfig05/EPIQ/Pages/MainPage?TaskId=
MultisessionURL320=https://cqmprdtstapp01.aurelius.com:561/epiqbyCaiber320V_TST/Pages/PlantSelection?ref=login
MultisessionSSOURL=http://cqmsconfig05/SSO/MainPage
SamplePDF=C:\\Users\\<USER>\\Desktop\\samplepdf.pdf
Docpath=System.getProperty("user.dir") + "Automation_Framework\Documents\1.pdf"
Reports=C://WF//AutomationReports//Reports//
Recordings=C://WF//AutomationReports//Recordings//
Screenshots=C://WF//Screenshots//
isReportedRequired=true

SomersetSSOURL=https://cqmprdtst1.aurelius.com:561/PL009200_SSO_GM_MGR_TST
SomersetSSOURLMultisession=https://cqmprdtst1.aurelius.com:561/PL009200_SSO_GM_MGR_TST/MainPage
SomersetURL=https://cqmprdtstapp01.aurelius.com:561/epiqByCaliber_310V_SP_TST/
somersetMultisessionURL=https://cqmprdtstapp01.aurelius.com:561/epiqByCaliber_310V_SP_TST/Pages/PlantSelection?ref=login

#plantSelection=SP3 Plant ( SPP1 )
#masterPlantSelection=SP MASTER PLANT ( SPPM )

#plantSelection=SP3 Plant ( SPP1 )
#masterPlantSelection=SP MASTER PLANT ( SPPM )


plantSelection=STL01 ( 1100 )


#Browsername Ex:ie,Chrome,firefox,edge
browser=Chrome

#Report Content
DocumentTitle=AutomationReportFs
ProductName=EPIQ LEARNIQ
Version=2.0
ProductCode=EPIQ310BL
URL=http://cqmsconfig05/EPIQ/
Name=Swetha Allampally
SystemName=swetha.ap
OS=Windows11
IPAddress=***********
HostName=CQML78184
ScenarioType=EndtoEnd


#ConfigFile
EnableAppendRandomValue=YES
ScreenCapture=YES
ScreenRecording=YES
HighlightElement=YES
ExecutionMode=fast

#Login Details
#310 version


company=SOMERSET
EpicUserID=anir3
EpicUserPWD=password1
SSoPWD=password
EPIQDefaultID=default
EPIQDefaultIPSW=password
EPIQApprovalID=saket3
EPIQApprovalPSW=password1
TRANSFERtoID=caluserSCZ
TRANSFERtoPWD=password090
TraineeID2=vijayiqm51
Traineepsw2=password1
CouInvRespID=vijayiqm51
CouInvRespPWD=password
CouInvRespIDRej=vijayiqm2
CouInvRespPWDRej=password
SelfNominateUserID=DawnR0J0
SelfNominateUserPSW=password

TraineeID=DawnEVCU
TraineepPWD=password

SSONewUserRegPassword=password0
EvaluatorUserID=Raju65
EvaluatorPWD=password9
EvaluatorUserName=raju47
EvaluatorPassword=password5
TraineeID21=Rituu1
Traineepsw21=1111b
TraineeIDQualified=vijayiqm51
TraineepswQualified=password1
TraineeIDToBeRetr=vijayiqm31
TraineepswToBeRetr=password1
FeedbackUserID=vijayiqm51
FeedbackUserPSW=password13
EPIQUserAcceptanceID=sessionnew2
EPIQUserAcceptancePSW=password
EPIQADUserID=saketh2
EPIQADUserPSW=password9

#320Version
company320=caif
EpicUserID320=vijayb1
EpicUserPWD320=password8
EPIQApprovalID320=shyam2
EPIQApprovalPSW320=password3
EPIQUserAcceptanceID=sessionnew61
EPIQUserAcceptancePSW=password
TraineeIDQualified320=reethu12
TraineepswQualified320=password
TraineeIDToBeRetr320=user101
TraineepswToBeRetr320=password1
CouInvRespID320=rithika2
CouInvRespPWD320=password1
CouInvRespIDRej320=rithika6
CouInvRespPWDRej320=password
SelfNominateUserID320=vijayb1
SelfNominateUserPSW320=password
ReportUser=vijaya.bj	
ReportPassword=Vijay@{12067
EPIQMDMApprovalID320=jhansi1
EPIQMDMApprovalPSW320=password3
SSONewUserRegPassword=password0
ADUserID=hardik16
ADPassword=password1

SSOUserID=saket3
SSOPassword=password0
EvaluatorUserName320=sessionuser1
EvaluatorPassword320=password1



# Dynamic Report Content Properties
click.stepDescription=Click on '{elementName}' {elementType}
click.acceptanceCriteria='{elementName}' should be clicked successfully and next screen should be displayed
click.actualResult='{elementName}' is getting clicked successfully and next screen is displayed
sendKeys.stepDescription=Enter '{inputValue}' in '{fieldName}' field
sendKeys.acceptanceCriteria=Entered value should be displayed in '{fieldName}' field
sendKeys.actualResult=Entered value is getting displayed in '{fieldName}' field

#Date
NextMonth=August
Nday=7s
NextYear=2021
RequalifyMonth=JULY
RequalifyYear=2024
RequalifyDay=6
PresentMonth=JULY
month=JULY
year=2024
day=26
DateFormat=dd MMM yyyy
Date=27 Aug 2050
TempPsw1=12345

#Remarks
ApproveRemarks=Approve Done
ModifyRemarks=Modification Done
ReturnRemarks=Returned
ReInitiateRemarks=Re-Initiated
StatusChangeRemarks=Status Changed
RITransferRemarks=RITransfered
NoValueRemarks=---
DropRemarks=Dropped
StandardRemarks=APPROVED
ConfigureRemarks=Configured
RegistrationRemarks= Registration Done
OJTremarks=OnJobTrainingNotRequired
NoRemarks=--
PDFUpload10Pages=\\QA_TestDocuments\\10pages.pdf
 
 