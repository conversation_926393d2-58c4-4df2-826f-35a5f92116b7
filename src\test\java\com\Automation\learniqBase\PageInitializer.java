package com.Automation.learniqBase;

import com.Automation.learniqObjects.*;

public class PageInitializer extends TestEngine {

	public static SSO_UserRegistration UserReg;
	public static SSO_UserProductModuleAssignment UserProductModuleAssignment;
	public static EPIQ_ResetPassword restpassword;
	public static EPICloginpage epiclogin;
	public static SYS_Subgroup Subgroup;
	public static SYS_Group Group;
	public static SYS_SubgroupAssignment SubgroupAssignment;
	
	public static DM_DocumentRegistration DocumentReg;
	public static CM_Topic InitiateTopic;
	public static CM_Course Initiate_Course;
	public static CM_TrainingSchedule TrainingShcedule;
	public static CM_CourseSession CourseSession;
	public static CM_RespondCourseInvitation CourseInvitation;
	public static CM_BatchFormation BatchFormation;
	public static CM_SelfNomination SelfNominate;
	public static CM_QuestionPaper questionPaper;
	public static CM_RecordAttendance RecordAttendance;
	public static CM_RespondQP RespondQP;
	public static CM_EvaluateAnswerPaper Evaluate;
	public static CM_Trainer trainer;
	public static CM_ProposeInductionTraining ProposeInduction;
	public static CM_Record_InductionTraining RecordInduction;
	public static CM_Respond_InductionTraining RespondInduction;
	public static CM_RecordMarks RecordMarks;
	public static CM_RecordDocumentReading RecordDR;
	public static CM_RespondDocumentReading RespondDR;
	public static CM_IndividualEmployeeReport IERReport;
	public static Signout Logout;
	public static CM_QuestionBank PrepareQB;
	public static CM_Completion_InductionTraining completionIT;
	public static CM_Reports reports;
	public static JRreport JRReport;
	public static CM_Respond_MissedQuestionAnalysis respondMQA;
	public static CM_RespondFeedback respondFeedback;
	public static CM_InterimGTP InterimGTP;
	public static CM_VerifyCourseSessionScreen verifyCSScreen;
	public static CM_CSRReport CSRReport;
	public static CM_CourseRetraining CourseRetraining;

	public static SetCentralConfiguration centralConfg;
	public static Print_JobReponsibility print_JR;
	public static SYS_JobResponsibility JobResponsibility;

	public static SYS_SubGroup_StatusChange subGroup_StatusChange;
	public static SYS_Group_Modification group_Modification;
	public static SYS_SubgroupModification Modify_SubGroup;
	public static SYS_Group_StatusChange sYS_Group_StatusChange;  
	public static SYS_Subgroup Initiate_SubGroup;
	public static CM_SelfStudyCourse selfStudyCourse;
	public static CM_Self_Study_QB SSQB;
	public static CM_TrainingVenueRegistration trainingVenueReg;


	public PageInitializer(String url) {

		super(url);
	}

	public PageInitializer() {

	}

	public static void initializePageObjects() {
		print_JR= new Print_JobReponsibility();
		
		centralConfg =new SetCentralConfiguration();
		subGroup_StatusChange = new SYS_SubGroup_StatusChange();
		group_Modification = new SYS_Group_Modification();

		Group = new SYS_Group();
		Modify_SubGroup = new SYS_SubgroupModification();
		sYS_Group_StatusChange = new SYS_Group_StatusChange();
		Initiate_SubGroup = new SYS_Subgroup();

		Initiate_Course = new CM_Course();
		epiclogin = new EPICloginpage();
		InitiateTopic = new CM_Topic();
		restpassword = new EPIQ_ResetPassword();
		Subgroup = new SYS_Subgroup();
		Group = new SYS_Group();
		SubgroupAssignment = new SYS_SubgroupAssignment();
		JobResponsibility = new SYS_JobResponsibility();
		DocumentReg = new DM_DocumentRegistration();
		TrainingShcedule = new CM_TrainingSchedule();
		CourseSession = new CM_CourseSession();
		CourseInvitation = new CM_RespondCourseInvitation();
		BatchFormation = new CM_BatchFormation();
		SelfNominate = new CM_SelfNomination();
		questionPaper = new CM_QuestionPaper();
		RecordAttendance = new CM_RecordAttendance();
		RespondQP = new CM_RespondQP();
		Evaluate = new CM_EvaluateAnswerPaper();
		trainer = new CM_Trainer();
		ProposeInduction = new CM_ProposeInductionTraining();
		RecordInduction = new CM_Record_InductionTraining();
		RespondInduction = new CM_Respond_InductionTraining();
		RecordMarks = new CM_RecordMarks();
		RespondDR = new CM_RespondDocumentReading();
		RecordDR = new CM_RecordDocumentReading();
		IERReport = new CM_IndividualEmployeeReport();
		Logout = new Signout();
		PrepareQB = new CM_QuestionBank();
		completionIT = new CM_Completion_InductionTraining();
		reports = new CM_Reports();
		UserReg = new SSO_UserRegistration();
		UserProductModuleAssignment = new SSO_UserProductModuleAssignment();
		JRReport = new JRreport();
		respondMQA = new CM_Respond_MissedQuestionAnalysis();
		respondFeedback = new CM_RespondFeedback();
		InterimGTP = new CM_InterimGTP();
		verifyCSScreen = new CM_VerifyCourseSessionScreen();
		CSRReport = new CM_CSRReport();
		CourseRetraining = new CM_CourseRetraining();
		selfStudyCourse = new CM_SelfStudyCourse();
		SSQB = new CM_Self_Study_QB();
		trainingVenueReg = new CM_TrainingVenueRegistration();

	}

}
