package com.Automation.Scripts;

import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.chrome.ChromeDriver;
import java.util.List;

/**
 * SUPER SIMPLE TRAINING VENUE SCRIPT
 * 
 * Uses only the most basic locators:
 * - (//input)[1], (//input)[2], etc.
 * - //button
 * - //textarea
 */
public class SuperSimpleScript {
    
    public static void main(String[] args) {
        WebDriver driver = new ChromeDriver();
        
        try {
            // Open page
            driver.get("http://cqmsconfig05/EPIQ/Pages/MainPage?TaskId=#");
            driver.manage().window().maximize();
            Thread.sleep(3000);
            
            // Fill form
            fillForm(driver);
            
            // Submit
            submit(driver);
            
            Thread.sleep(5000);
            
        } catch (Exception e) {
            e.printStackTrace();
        }
        // driver.quit();
    }
    
    private static void fillForm(WebDriver driver) {
        // Test data
        String[] data = {
            "Conference Hall A",           // Field 1
            "Room-101",                   // Field 2
            "50",                         // Field 3
            "9876543210",                 // Field 4
            "123 Business Park",          // Field 5
            "New York",                   // Field 6
            "10001",                      // Field 7
            "100",                        // Field 8
            "VENUE001",                   // Field 9
            "Conference Room",            // Field 10
            "John Doe",                   // Field 11
            "<EMAIL>",       // Field 12
            "Building A, Floor 2",        // Field 13
            "NY"                          // Field 14
        };
        
        // Fill inputs by position
        for (int i = 1; i <= data.length; i++) {
            try {
                WebElement field = driver.findElement(By.xpath("(//input)[" + i + "]"));
                field.clear();
                field.sendKeys(data[i-1]);
                System.out.println("Field " + i + ": " + data[i-1]);
                Thread.sleep(300);
            } catch (Exception e) {
                System.out.println("Failed field " + i);
            }
        }
        
        // Fill textareas
        try {
            List<WebElement> textareas = driver.findElements(By.xpath("//textarea"));
            if (textareas.size() > 0) {
                textareas.get(0).sendKeys("Projector, Whiteboard, Audio System");
                System.out.println("Textarea 1: Equipment");
            }
            if (textareas.size() > 1) {
                textareas.get(1).sendKeys("Air conditioned room with parking facility");
                System.out.println("Textarea 2: Additional Info");
            }
        } catch (Exception e) {
            System.out.println("Textarea failed");
        }
    }
    
    private static void submit(WebDriver driver) {
        try {
            WebElement button = driver.findElement(By.xpath("//button"));
            button.click();
            System.out.println("Submit clicked");
        } catch (Exception e) {
            System.out.println("Submit failed");
        }
    }
}
