package com.Automation.Scripts;

import org.openqa.selenium.By;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.openqa.selenium.support.ui.ExpectedConditions;
import java.time.Duration;
import java.util.List;

/**
 * Robust Training Venue Registration Script
 * Uses multiple strategies to ensure form filling works
 */
public class RobustTrainingVenueScript {
    
    private static WebDriver driver;
    private static WebDriverWait wait;
    private static JavascriptExecutor js;
    
    public static void main(String[] args) {
        try {
            initializeDriver();
            navigateToPage();
            
            System.out.println("=== Starting Training Venue Registration ===");
            
            // Try multiple strategies in order of preference
            boolean success = false;
            
            // Strategy 1: Try to identify form structure
            success = analyzeAndFillForm();
            
            if (!success) {
                System.out.println("Strategy 1 failed, trying Strategy 2...");
                success = fillByElementInspection();
            }
            
            if (!success) {
                System.out.println("Strategy 2 failed, trying Strategy 3...");
                success = fillByJavaScript();
            }
            
            if (success) {
                submitForm();
                System.out.println("=== Form submission completed ===");
            } else {
                System.out.println("=== All strategies failed ===");
            }
            
            // Wait to see results
            Thread.sleep(5000);
            
        } catch (Exception e) {
            System.out.println("Error: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // Uncomment to close browser
            // driver.quit();
        }
    }
    
    private static void initializeDriver() {
        driver = new ChromeDriver();
        wait = new WebDriverWait(driver, Duration.ofSeconds(15));
        js = (JavascriptExecutor) driver;
        driver.manage().window().maximize();
    }
    
    private static void navigateToPage() {
        driver.get("http://cqmsconfig05/EPIQ/Pages/MainPage?TaskId=#");
        
        // Wait for page to load completely
        wait.until(ExpectedConditions.jsReturnsValue("return document.readyState === 'complete'"));
        
        // Additional wait for dynamic content
        try {
            Thread.sleep(3000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * Strategy 1: Analyze form structure and fill intelligently
     */
    private static boolean analyzeAndFillForm() {
        try {
            System.out.println("Strategy 1: Analyzing form structure...");
            
            // Look for form containers
            List<WebElement> containers = driver.findElements(By.xpath(
                "//form | //div[contains(@class,'form')] | //table | //div[contains(@class,'container')]"));
            
            if (containers.isEmpty()) {
                System.out.println("No form containers found");
                return false;
            }
            
            System.out.println("Found " + containers.size() + " potential form container(s)");
            
            // Get all visible input fields
            List<WebElement> inputs = driver.findElements(By.xpath(
                "//input[@type='text' or not(@type) or @type=''] | //textarea"));
            
            // Filter for visible and enabled fields
            inputs.removeIf(element -> {
                try {
                    return !element.isDisplayed() || !element.isEnabled();
                } catch (Exception e) {
                    return true;
                }
            });
            
            System.out.println("Found " + inputs.size() + " visible input fields");
            
            if (inputs.size() < 7) { // Minimum expected fields
                System.out.println("Not enough input fields found");
                return false;
            }
            
            // Fill the fields with test data
            String[] testData = getTestData();
            
            for (int i = 0; i < Math.min(inputs.size(), testData.length); i++) {
                try {
                    WebElement field = inputs.get(i);
                    
                    // Scroll to element
                    js.executeScript("arguments[0].scrollIntoView(true);", field);
                    Thread.sleep(200);
                    
                    // Clear and fill
                    field.clear();
                    field.sendKeys(testData[i]);
                    
                    System.out.println("Filled field " + (i+1) + ": " + testData[i]);
                    Thread.sleep(300);
                    
                } catch (Exception e) {
                    System.out.println("Error filling field " + (i+1) + ": " + e.getMessage());
                }
            }
            
            return true;
            
        } catch (Exception e) {
            System.out.println("Strategy 1 error: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Strategy 2: Inspect each element individually
     */
    private static boolean fillByElementInspection() {
        try {
            System.out.println("Strategy 2: Individual element inspection...");
            
            // Get all input elements on the page
            List<WebElement> allElements = driver.findElements(By.xpath("//*"));
            List<WebElement> inputElements = new java.util.ArrayList<>();
            
            // Find input-like elements
            for (WebElement element : allElements) {
                try {
                    String tagName = element.getTagName().toLowerCase();
                    String type = element.getAttribute("type");
                    
                    if ((tagName.equals("input") && (type == null || type.equals("text") || type.equals(""))) ||
                        tagName.equals("textarea")) {
                        
                        if (element.isDisplayed() && element.isEnabled()) {
                            inputElements.add(element);
                        }
                    }
                } catch (Exception e) {
                    continue;
                }
            }
            
            System.out.println("Found " + inputElements.size() + " input elements");
            
            if (inputElements.size() < 5) {
                return false;
            }
            
            String[] testData = getTestData();
            
            for (int i = 0; i < Math.min(inputElements.size(), testData.length); i++) {
                fillElementSafely(inputElements.get(i), testData[i], "Element " + (i+1));
            }
            
            return true;
            
        } catch (Exception e) {
            System.out.println("Strategy 2 error: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Strategy 3: Use JavaScript to fill form
     */
    private static boolean fillByJavaScript() {
        try {
            System.out.println("Strategy 3: JavaScript-based filling...");
            
            // Use JavaScript to find and fill inputs
            String script = 
                "var inputs = document.querySelectorAll('input[type=\"text\"], input:not([type]), textarea');" +
                "var visibleInputs = [];" +
                "for(var i = 0; i < inputs.length; i++) {" +
                "  var style = window.getComputedStyle(inputs[i]);" +
                "  if(style.display !== 'none' && style.visibility !== 'hidden' && inputs[i].offsetParent !== null) {" +
                "    visibleInputs.push(inputs[i]);" +
                "  }" +
                "}" +
                "return visibleInputs.length;";
            
            Long inputCount = (Long) js.executeScript(script);
            System.out.println("JavaScript found " + inputCount + " visible inputs");
            
            if (inputCount < 5) {
                return false;
            }
            
            String[] testData = getTestData();
            
            for (int i = 0; i < Math.min(inputCount.intValue(), testData.length); i++) {
                String fillScript = 
                    "var inputs = document.querySelectorAll('input[type=\"text\"], input:not([type]), textarea');" +
                    "var visibleInputs = [];" +
                    "for(var j = 0; j < inputs.length; j++) {" +
                    "  var style = window.getComputedStyle(inputs[j]);" +
                    "  if(style.display !== 'none' && style.visibility !== 'hidden' && inputs[j].offsetParent !== null) {" +
                    "    visibleInputs.push(inputs[j]);" +
                    "  }" +
                    "}" +
                    "if(visibleInputs[" + i + "]) {" +
                    "  visibleInputs[" + i + "].value = '" + testData[i] + "';" +
                    "  visibleInputs[" + i + "].dispatchEvent(new Event('input', { bubbles: true }));" +
                    "  visibleInputs[" + i + "].dispatchEvent(new Event('change', { bubbles: true }));" +
                    "  return true;" +
                    "}" +
                    "return false;";
                
                Boolean filled = (Boolean) js.executeScript(fillScript);
                if (filled) {
                    System.out.println("JS filled field " + (i+1) + ": " + testData[i]);
                }
                
                Thread.sleep(200);
            }
            
            return true;
            
        } catch (Exception e) {
            System.out.println("Strategy 3 error: " + e.getMessage());
            return false;
        }
    }
    
    private static void fillElementSafely(WebElement element, String value, String fieldName) {
        try {
            // Scroll to element
            js.executeScript("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", element);
            Thread.sleep(300);
            
            // Try multiple ways to fill
            try {
                element.clear();
                element.sendKeys(value);
            } catch (Exception e1) {
                try {
                    js.executeScript("arguments[0].value = arguments[1];", element, value);
                    js.executeScript("arguments[0].dispatchEvent(new Event('input', { bubbles: true }));", element);
                } catch (Exception e2) {
                    System.out.println("Could not fill " + fieldName + ": " + e2.getMessage());
                    return;
                }
            }
            
            System.out.println("Filled " + fieldName + ": " + value);
            Thread.sleep(200);
            
        } catch (Exception e) {
            System.out.println("Error filling " + fieldName + ": " + e.getMessage());
        }
    }
    
    private static void submitForm() {
        try {
            System.out.println("Attempting to submit form...");
            
            // Try multiple submit strategies
            String[] submitSelectors = {
                "//button[text()='Submit']",
                "//input[@type='submit']",
                "//button[@id='btnSubmit']",
                "//input[@value='Submit']",
                "//button[contains(text(),'Submit')]",
                "//button[contains(@class,'submit')]"
            };
            
            for (String selector : submitSelectors) {
                try {
                    WebElement submitBtn = driver.findElement(By.xpath(selector));
                    if (submitBtn.isDisplayed() && submitBtn.isEnabled()) {
                        js.executeScript("arguments[0].scrollIntoView(true);", submitBtn);
                        Thread.sleep(500);
                        submitBtn.click();
                        System.out.println("Submit button clicked using: " + selector);
                        return;
                    }
                } catch (Exception e) {
                    continue;
                }
            }
            
            // Try JavaScript submit
            js.executeScript("document.querySelector('form').submit();");
            System.out.println("Form submitted using JavaScript");
            
        } catch (Exception e) {
            System.out.println("Could not submit form: " + e.getMessage());
        }
    }
    
    private static String[] getTestData() {
        return new String[] {
            "Conference Hall A",           // Training Venue Name
            "Room-101",                   // Room Number
            "50",                         // Capacity
            "9876543210",                 // Phone Number
            "123 Business Park",          // Address1
            "New York",                   // City
            "10001",                      // Pin/Zip
            "100",                        // Cost Per Hour
            "VENUE001",                   // Unique Code
            "Conference Room",            // Room Type
            "John Doe",                   // Contact Person
            "<EMAIL>",       // Email ID
            "Building A, Floor 2",        // Address2
            "NY",                         // State
            "Projector, Whiteboard, Audio System",  // Room Equipment
            "Air conditioned room with parking facility"  // Additional Info
        };
    }
}
