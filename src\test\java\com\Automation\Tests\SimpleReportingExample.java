package com.Automation.Tests;

import org.testng.annotations.*;
import com.Automation.learniqBase.OQActionEngine;

/**
 * Example showing how to use simple reporting without changing existing test cases
 * Just add OQActionEngine.enableSimpleReporting() in @BeforeMethod
 */
public class SimpleReportingExample extends OQActionEngine {
    
    @BeforeMethod
    public void setupSimpleReporting() {
        // Enable simple reporting mode - this affects ALL click2 and sendKeys2 calls
        OQActionEngine.enableSimpleReporting();
        System.out.println("Simple reporting enabled for this test");
    }
    
    @AfterMethod
    public void resetReporting() {
        // Optional: Reset to detailed reporting after test
        // OQActionEngine.disableSimpleReporting();
    }
    
    @Test
    public void exampleTestWithSimpleReporting() {
        // Your existing test code works exactly the same!
        // But now generates simple reports instead of detailed tables
        
        // Example: Your existing click2 calls work unchanged
        // click2(loginButton, "Click Login", "But<PERSON> should be clicked", "But<PERSON> clicked", "login_btn");
        // This will now log: "✓ Clicked - login btn" instead of a detailed table
        
        // Example: Your existing sendKeys2 calls work unchanged  
        // sendKeys2(usernameField, "Enter username", "testuser", "Username should be entered", "Username entered", "username_field");
        // This will now log: "✓ Entered 'testuser' in - username field" instead of a detailed table
        
        System.out.println("Test completed with simple reporting");
    }
    
    @Test
    public void exampleTestWithDetailedReporting() {
        // Disable simple reporting for this specific test
        OQActionEngine.disableSimpleReporting();
        
        // Now your click2 and sendKeys2 calls will use detailed table reporting
        // click2(loginButton, "Click Login", "Button should be clicked", "Button clicked", "login_btn");
        // This will generate the original detailed table with Step Description, Acceptance Criteria, etc.
        
        System.out.println("Test completed with detailed reporting");
    }
}
