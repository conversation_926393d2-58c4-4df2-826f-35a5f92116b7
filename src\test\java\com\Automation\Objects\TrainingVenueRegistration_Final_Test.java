package com.Automation.Objects;

import java.util.HashMap;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import com.Automation.learniqBase.PageInitializer;
import com.Automation.learniqObjects.CM_TrainingVenueRegistration_Final;
import com.Automation.learniqObjects.EPICloginpage;
import com.Automation.learniqObjects.Logout;
import com.Automation.Utils.ConfigsReader;

/**
 * Training Venue Registration Test Class - Final Version
 * Uses name/id locators for reliable automation
 */
public class TrainingVenueRegistration_Final_Test extends PageInitializer {

    private CM_TrainingVenueRegistration_Final trainingVenueReg;
    private EPICloginpage epiclogin;

    @BeforeMethod
    public void setUp() {
        // Initialize page objects
        trainingVenueReg = new CM_TrainingVenueRegistration_Final();
        epiclogin = new EPICloginpage();
    }

    @Test(priority = 1, description = "Training Venue Registration - Mandatory Fields Only")
    public void testTrainingVenueRegistrationMandatoryFields() {
        System.out.println("=== Test: Training Venue Registration - Mandatory Fields ===");

        // Create minimal test data
        HashMap<String, String> testData = new HashMap<>();
        testData.put("trainingVenueName", "Conference Hall A");
        testData.put("roomNumber", "Room-101");
        testData.put("capacity", "50");
        testData.put("phoneNumber", "9876543210");
        testData.put("uniqueCode", "VENUE001");
        testData.put("roomType", "Conference Room");
        testData.put("contactPerson", "John Doe");

        // Login to application
        epiclogin.loginToApplication(
                ConfigsReader.getPropValue("company"), 
                ConfigsReader.getPropValue("EpicUserID"),
                ConfigsReader.getPropValue("EpicUserPWD")
        );
        
        // Select plant
        epiclogin.plant1();

        // Navigate to Training Venue Registration and fill form
        trainingVenueReg.submitTrainingVenueForm(testData);

        // Logout
        Logout.signOutPage();
        
        System.out.println("=== Mandatory Fields Test Completed ===");
    }

    @Test(priority = 2, description = "Training Venue Registration - All Fields")
    public void testTrainingVenueRegistrationAllFields() {
        System.out.println("=== Test: Training Venue Registration - All Fields ===");

        // Create complete test data
        HashMap<String, String> testData = new HashMap<>();
        testData.put("trainingVenueName", "Training Center B");
        testData.put("roomNumber", "Room-102");
        testData.put("capacity", "25");
        testData.put("phoneNumber", "9876543211");
        testData.put("uniqueCode", "VENUE002");
        testData.put("roomType", "Training Room");
        testData.put("contactPerson", "Jane Smith");
        testData.put("address1", "123 Business Park");
        testData.put("city", "New York");
        testData.put("pinZip", "10001");
        testData.put("costPerHour", "100");
        testData.put("emailId", "<EMAIL>");
        testData.put("address2", "Building A, Floor 2");
        testData.put("state", "NY");
        testData.put("roomEquipment", "Projector, Whiteboard, Audio System");
        testData.put("additionalInfo", "Air conditioned room with parking facility");

        // Login to application
        epiclogin.loginToApplication(
                ConfigsReader.getPropValue("company"), 
                ConfigsReader.getPropValue("EpicUserID"),
                ConfigsReader.getPropValue("EpicUserPWD")
        );
        
        // Select plant
        epiclogin.plant1();

        // Navigate to Training Venue Registration and fill form
        trainingVenueReg.submitTrainingVenueForm(testData);

        // Logout
        Logout.signOutPage();
        
        System.out.println("=== All Fields Test Completed ===");
    }

    @Test(priority = 3, description = "Training Venue Registration - Executive Room")
    public void testTrainingVenueRegistrationExecutiveRoom() {
        System.out.println("=== Test: Training Venue Registration - Executive Room ===");

        // Create executive room test data
        HashMap<String, String> testData = new HashMap<>();
        testData.put("trainingVenueName", "Executive Boardroom");
        testData.put("roomNumber", "EXEC-001");
        testData.put("capacity", "12");
        testData.put("phoneNumber", "9876543212");
        testData.put("uniqueCode", "EXEC001");
        testData.put("roomType", "Executive Boardroom");
        testData.put("contactPerson", "Robert Wilson");
        testData.put("address1", "456 Corporate Plaza");
        testData.put("city", "Chicago");
        testData.put("pinZip", "60601");
        testData.put("costPerHour", "200");
        testData.put("emailId", "<EMAIL>");
        testData.put("address2", "Tower A, 20th Floor");
        testData.put("state", "IL");
        testData.put("roomEquipment", "4K Display, Video Conferencing, Executive Seating");
        testData.put("additionalInfo", "Premium executive boardroom with butler service and valet parking");

        // Login to application
        epiclogin.loginToApplication(
                ConfigsReader.getPropValue("company"), 
                ConfigsReader.getPropValue("EpicUserID"),
                ConfigsReader.getPropValue("EpicUserPWD")
        );
        
        // Select plant
        epiclogin.plant1();

        // Navigate to Training Venue Registration and fill form
        trainingVenueReg.submitTrainingVenueForm(testData);

        // Logout
        Logout.signOutPage();
        
        System.out.println("=== Executive Room Test Completed ===");
    }

    @AfterMethod
    public void tearDown() {
        // Any cleanup if needed
        System.out.println("Test method completed");
    }
}
