package com.Automation.Utils;

import java.io.FileWriter;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Simple HTML Report Generator
 * Creates basic HTML reports without complex formatting
 */
public class SimpleReportGenerator {
    
    private static List<TestStep> testSteps = new ArrayList<>();
    private static String testName = "";
    private static Date startTime;
    private static String reportPath = "./test-output/simple-report.html";
    
    public static void startTest(String name) {
        testName = name;
        startTime = new Date();
        testSteps.clear();
    }
    
    public static void logAction(String action, String element, boolean passed) {
        testSteps.add(new TestStep(action, element, passed, new Date()));
    }
    
    public static void logAction(String action, boolean passed) {
        testSteps.add(new TestStep(action, "", passed, new Date()));
    }
    
    public static void generateReport() {
        try (FileWriter writer = new FileWriter(reportPath)) {
            writer.write(generateHTML());
            System.out.println("Simple report generated: " + reportPath);
        } catch (IOException e) {
            System.err.println("Failed to generate report: " + e.getMessage());
        }
    }
    
    private static String generateHTML() {
        StringBuilder html = new StringBuilder();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        
        html.append("<!DOCTYPE html>\n");
        html.append("<html>\n<head>\n");
        html.append("<title>Test Report - ").append(testName).append("</title>\n");
        html.append("<style>\n");
        html.append("body { font-family: Arial, sans-serif; margin: 20px; }\n");
        html.append("h1 { color: #333; }\n");
        html.append("table { border-collapse: collapse; width: 100%; }\n");
        html.append("th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }\n");
        html.append("th { background-color: #f2f2f2; }\n");
        html.append(".pass { color: green; }\n");
        html.append(".fail { color: red; }\n");
        html.append(".summary { background-color: #f9f9f9; padding: 10px; margin: 10px 0; }\n");
        html.append("</style>\n");
        html.append("</head>\n<body>\n");
        
        // Header
        html.append("<h1>Test Report: ").append(testName).append("</h1>\n");
        html.append("<div class='summary'>\n");
        html.append("<p><strong>Start Time:</strong> ").append(dateFormat.format(startTime)).append("</p>\n");
        html.append("<p><strong>End Time:</strong> ").append(dateFormat.format(new Date())).append("</p>\n");
        html.append("<p><strong>Total Steps:</strong> ").append(testSteps.size()).append("</p>\n");
        
        long passCount = testSteps.stream().mapToLong(step -> step.passed ? 1 : 0).sum();
        long failCount = testSteps.size() - passCount;
        
        html.append("<p><strong>Passed:</strong> <span class='pass'>").append(passCount).append("</span></p>\n");
        html.append("<p><strong>Failed:</strong> <span class='fail'>").append(failCount).append("</span></p>\n");
        html.append("</div>\n");
        
        // Test Steps Table
        html.append("<h2>Test Steps</h2>\n");
        html.append("<table>\n");
        html.append("<tr><th>Step</th><th>Action</th><th>Element</th><th>Status</th><th>Time</th></tr>\n");
        
        for (int i = 0; i < testSteps.size(); i++) {
            TestStep step = testSteps.get(i);
            html.append("<tr>\n");
            html.append("<td>").append(i + 1).append("</td>\n");
            html.append("<td>").append(step.action).append("</td>\n");
            html.append("<td>").append(step.element).append("</td>\n");
            html.append("<td class='").append(step.passed ? "pass" : "fail").append("'>")
                .append(step.passed ? "PASS" : "FAIL").append("</td>\n");
            html.append("<td>").append(dateFormat.format(step.timestamp)).append("</td>\n");
            html.append("</tr>\n");
        }
        
        html.append("</table>\n");
        html.append("</body>\n</html>");
        
        return html.toString();
    }
    
    private static class TestStep {
        String action;
        String element;
        boolean passed;
        Date timestamp;
        
        TestStep(String action, String element, boolean passed, Date timestamp) {
            this.action = action;
            this.element = element;
            this.passed = passed;
            this.timestamp = timestamp;
        }
    }
}
