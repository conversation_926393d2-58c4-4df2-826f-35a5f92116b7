package com.Automation.Scripts;

import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.chrome.ChromeDriver;
import java.time.Duration;

/**
 * SIMPLE TRAINING VENUE SCRIPT - BASIC LOCATORS ONLY
 * 
 * Uses the most basic and common locator strategies:
 * 1. By position (1st input, 2nd input, etc.)
 * 2. By tag name
 * 3. By simple XPath
 */
public class SimpleLocatorsScript {
    
    public static void main(String[] args) {
        WebDriver driver = new ChromeDriver();
        driver.manage().timeouts().implicitlyWait(Duration.ofSeconds(10));
        
        try {
            // Navigate to page
            driver.get("http://cqmsconfig05/EPIQ/Pages/MainPage?TaskId=#");
            driver.manage().window().maximize();
            Thread.sleep(3000);
            
            System.out.println("=== SIMPLE TRAINING VENUE FORM FILLING ===");
            
            // Method 1: Fill by position (most reliable)
            fillByPosition(driver);
            
            // Method 2: Submit form
            submitForm(driver);
            
            System.out.println("=== COMPLETED ===");
            Thread.sleep(5000);
            
        } catch (Exception e) {
            System.out.println("Error: " + e.getMessage());
        } finally {
            // driver.quit(); // Uncomment to close browser
        }
    }
    
    /**
     * Fill form by input position - SIMPLEST METHOD
     */
    private static void fillByPosition(WebDriver driver) {
        String[] values = {
            "Conference Hall A",           // 1st field
            "Room-101",                   // 2nd field  
            "50",                         // 3rd field
            "9876543210",                 // 4th field
            "123 Business Park",          // 5th field
            "New York",                   // 6th field
            "10001",                      // 7th field
            "100",                        // 8th field
            "VENUE001",                   // 9th field
            "Conference Room",            // 10th field
            "John Doe",                   // 11th field
            "<EMAIL>",       // 12th field
            "Building A, Floor 2",        // 13th field
            "NY"                          // 14th field
        };
        
        // Fill input fields by position
        for (int i = 1; i <= values.length; i++) {
            try {
                // Try multiple simple locators for each position
                WebElement field = null;
                
                // Locator 1: By position in all inputs
                try {
                    field = driver.findElement(By.xpath("(//input)[" + i + "]"));
                } catch (Exception e1) {
                    // Locator 2: By position in text inputs only
                    try {
                        field = driver.findElement(By.xpath("(//input[@type='text'])[" + i + "]"));
                    } catch (Exception e2) {
                        // Locator 3: By position in any input without type
                        try {
                            field = driver.findElement(By.xpath("(//input[not(@type) or @type='text'])[" + i + "]"));
                        } catch (Exception e3) {
                            System.out.println("Could not find field " + i);
                            continue;
                        }
                    }
                }
                
                // Fill the field
                if (field != null && field.isDisplayed() && field.isEnabled()) {
                    field.clear();
                    field.sendKeys(values[i-1]);
                    System.out.println("✓ Field " + i + ": " + values[i-1]);
                    Thread.sleep(300);
                }
                
            } catch (Exception e) {
                System.out.println("✗ Failed field " + i + ": " + e.getMessage());
            }
        }
        
        // Fill textarea fields
        fillTextAreas(driver);
    }
    
    /**
     * Fill textarea fields
     */
    private static void fillTextAreas(WebDriver driver) {
        String[] textareaValues = {
            "Projector, Whiteboard, Audio System",
            "Air conditioned room with parking facility"
        };
        
        try {
            // Find all textareas
            java.util.List<WebElement> textareas = driver.findElements(By.tagName("textarea"));
            
            for (int i = 0; i < Math.min(textareas.size(), textareaValues.length); i++) {
                WebElement textarea = textareas.get(i);
                if (textarea.isDisplayed() && textarea.isEnabled()) {
                    textarea.clear();
                    textarea.sendKeys(textareaValues[i]);
                    System.out.println("✓ Textarea " + (i+1) + ": " + textareaValues[i]);
                    Thread.sleep(300);
                }
            }
        } catch (Exception e) {
            System.out.println("✗ Failed to fill textareas: " + e.getMessage());
        }
    }
    
    /**
     * Submit form using simple locators
     */
    private static void submitForm(WebDriver driver) {
        try {
            WebElement submitButton = null;
            
            // Try simple submit locators
            try {
                submitButton = driver.findElement(By.xpath("//button[text()='Submit']"));
            } catch (Exception e1) {
                try {
                    submitButton = driver.findElement(By.xpath("//input[@type='submit']"));
                } catch (Exception e2) {
                    try {
                        submitButton = driver.findElement(By.xpath("//button"));
                    } catch (Exception e3) {
                        System.out.println("✗ Could not find submit button");
                        return;
                    }
                }
            }
            
            if (submitButton != null && submitButton.isDisplayed() && submitButton.isEnabled()) {
                submitButton.click();
                System.out.println("✓ Submit button clicked");
            }
            
        } catch (Exception e) {
            System.out.println("✗ Submit failed: " + e.getMessage());
        }
    }
}

/**
 * ALTERNATIVE SIMPLE METHODS
 */
class AlternativeSimpleMethods {
    
    /**
     * Method A: Fill all inputs in order
     */
    public static void fillAllInputsInOrder(WebDriver driver) {
        String[] values = {
            "Conference Hall A", "Room-101", "50", "9876543210", 
            "123 Business Park", "New York", "10001", "100",
            "VENUE001", "Conference Room", "John Doe", "<EMAIL>",
            "Building A, Floor 2", "NY"
        };
        
        try {
            java.util.List<WebElement> inputs = driver.findElements(By.tagName("input"));
            
            int valueIndex = 0;
            for (WebElement input : inputs) {
                if (valueIndex >= values.length) break;
                
                try {
                    String type = input.getAttribute("type");
                    if (type == null || type.equals("text") || type.equals("")) {
                        if (input.isDisplayed() && input.isEnabled()) {
                            input.clear();
                            input.sendKeys(values[valueIndex]);
                            System.out.println("Filled: " + values[valueIndex]);
                            valueIndex++;
                            Thread.sleep(200);
                        }
                    }
                } catch (Exception e) {
                    continue;
                }
            }
        } catch (Exception e) {
            System.out.println("Error: " + e.getMessage());
        }
    }
    
    /**
     * Method B: Use CSS selectors
     */
    public static void fillUsingCSS(WebDriver driver) {
        String[] cssSelectors = {
            "input:nth-of-type(1)", "input:nth-of-type(2)", "input:nth-of-type(3)",
            "input:nth-of-type(4)", "input:nth-of-type(5)", "input:nth-of-type(6)",
            "input:nth-of-type(7)", "input:nth-of-type(8)", "input:nth-of-type(9)",
            "input:nth-of-type(10)", "input:nth-of-type(11)", "input:nth-of-type(12)",
            "input:nth-of-type(13)", "input:nth-of-type(14)"
        };
        
        String[] values = {
            "Conference Hall A", "Room-101", "50", "9876543210",
            "123 Business Park", "New York", "10001", "100", 
            "VENUE001", "Conference Room", "John Doe", "<EMAIL>",
            "Building A, Floor 2", "NY"
        };
        
        for (int i = 0; i < cssSelectors.length && i < values.length; i++) {
            try {
                WebElement field = driver.findElement(By.cssSelector(cssSelectors[i]));
                if (field.isDisplayed() && field.isEnabled()) {
                    field.clear();
                    field.sendKeys(values[i]);
                    System.out.println("CSS filled: " + values[i]);
                    Thread.sleep(200);
                }
            } catch (Exception e) {
                System.out.println("CSS failed for field " + (i+1));
            }
        }
    }
    
    /**
     * Method C: One-liner locators
     */
    public static void fillWithOneLiners(WebDriver driver) {
        try {
            // Super simple - just find inputs and fill them
            java.util.List<WebElement> allInputs = driver.findElements(By.xpath("//input | //textarea"));
            String[] data = {"Conference Hall A", "Room-101", "50", "9876543210", "123 Business Park", 
                           "New York", "10001", "100", "VENUE001", "Conference Room", "John Doe", 
                           "<EMAIL>", "Building A, Floor 2", "NY", 
                           "Projector, Whiteboard, Audio System", "Air conditioned room"};
            
            for (int i = 0; i < Math.min(allInputs.size(), data.length); i++) {
                try {
                    if (allInputs.get(i).isDisplayed()) {
                        allInputs.get(i).sendKeys(data[i]);
                        Thread.sleep(100);
                    }
                } catch (Exception e) { /* ignore */ }
            }
            
            // Submit
            driver.findElement(By.xpath("//button | //input[@type='submit']")).click();
            
        } catch (Exception e) {
            System.out.println("One-liner method failed: " + e.getMessage());
        }
    }
}
