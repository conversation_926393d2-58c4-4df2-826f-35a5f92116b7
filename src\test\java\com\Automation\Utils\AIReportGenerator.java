package com.Automation.Utils;

import org.openqa.selenium.WebElement;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.net.URI;
import java.util.Map;
import java.util.HashMap;
import java.util.concurrent.CompletableFuture;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * AI-Powered Report Content Generator
 * Uses LLM APIs to generate intelligent, context-aware report content
 */
public class AIReportGenerator {
    
    private static final HttpClient httpClient = HttpClient.newHttpClient();
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final String OPENAI_API_KEY = System.getProperty("openai.api.key", "your-api-key");
    private static final String OPENAI_API_URL = "https://api.openai.com/v1/chat/completions";
    
    /**
     * Generate intelligent report content using AI
     */
    public static CompletableFuture<AIReportData> generateReportContentAsync(
            String action, WebElement element, String pageContext, String businessContext) {
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                String elementInfo = analyzeElement(element);
                String prompt = buildPrompt(action, elementInfo, pageContext, businessContext);
                
                Map<String, Object> requestBody = buildOpenAIRequest(prompt);
                String jsonRequest = objectMapper.writeValueAsString(requestBody);
                
                HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(OPENAI_API_URL))
                    .header("Content-Type", "application/json")
                    .header("Authorization", "Bearer " + OPENAI_API_KEY)
                    .POST(HttpRequest.BodyPublishers.ofString(jsonRequest))
                    .build();
                
                HttpResponse<String> response = httpClient.send(request, 
                    HttpResponse.BodyHandlers.ofString());
                
                if (response.statusCode() == 200) {
                    return parseOpenAIResponse(response.body());
                } else {
                    return getFallbackReportData(action, element);
                }
                
            } catch (Exception e) {
                System.err.println("AI report generation failed: " + e.getMessage());
                return getFallbackReportData(action, element);
            }
        });
    }
    
    /**
     * Synchronous version for immediate use
     */
    public static AIReportData generateReportContent(String action, WebElement element, 
                                                   String pageContext, String businessContext) {
        try {
            return generateReportContentAsync(action, element, pageContext, businessContext).get();
        } catch (Exception e) {
            return getFallbackReportData(action, element);
        }
    }
    
    private static String analyzeElement(WebElement element) {
        StringBuilder analysis = new StringBuilder();
        
        try {
            analysis.append("Element Type: ").append(element.getTagName()).append("\n");
            
            String id = element.getAttribute("id");
            if (id != null && !id.isEmpty()) {
                analysis.append("ID: ").append(id).append("\n");
            }
            
            String className = element.getAttribute("class");
            if (className != null && !className.isEmpty()) {
                analysis.append("CSS Classes: ").append(className).append("\n");
            }
            
            String text = element.getText();
            if (text != null && !text.isEmpty() && text.length() < 100) {
                analysis.append("Text Content: ").append(text).append("\n");
            }
            
            String placeholder = element.getAttribute("placeholder");
            if (placeholder != null && !placeholder.isEmpty()) {
                analysis.append("Placeholder: ").append(placeholder).append("\n");
            }
            
            String type = element.getAttribute("type");
            if (type != null && !type.isEmpty()) {
                analysis.append("Input Type: ").append(type).append("\n");
            }
            
            String role = element.getAttribute("role");
            if (role != null && !role.isEmpty()) {
                analysis.append("ARIA Role: ").append(role).append("\n");
            }
            
        } catch (Exception e) {
            analysis.append("Element analysis failed: ").append(e.getMessage());
        }
        
        return analysis.toString();
    }
    
    private static String buildPrompt(String action, String elementInfo, String pageContext, String businessContext) {
        return String.format("""
            You are a QA automation expert writing test step documentation. Generate professional, clear, and concise test step content.
            
            Context:
            - Action: %s
            - Page Context: %s
            - Business Context: %s
            - Element Information:
            %s
            
            Please generate:
            1. Step Description: A clear description of what the test step does
            2. Acceptance Criteria: What should happen when this step is executed successfully
            3. Actual Result: What actually happens when the step is executed (present tense)
            4. Screenshot Name: A descriptive name for the screenshot (snake_case format)
            5. Business Value: Why this step is important from a business perspective
            6. User Story: A brief user story context for this action
            
            Format your response as JSON with these exact keys:
            {
                "stepDescription": "...",
                "acceptanceCriteria": "...",
                "actualResult": "...",
                "screenshotName": "...",
                "businessValue": "...",
                "userStory": "..."
            }
            
            Guidelines:
            - Keep descriptions concise but informative
            - Use professional testing language
            - Make acceptance criteria specific and measurable
            - Ensure actual result is in present continuous tense
            - Screenshot names should be descriptive and use snake_case
            - Business value should explain the user/business benefit
            - User story should follow "As a [user], I want [goal] so that [benefit]" format
            """, action, pageContext, businessContext, elementInfo);
    }
    
    private static Map<String, Object> buildOpenAIRequest(String prompt) {
        Map<String, Object> message = new HashMap<>();
        message.put("role", "user");
        message.put("content", prompt);
        
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("model", "gpt-3.5-turbo");
        requestBody.put("messages", new Object[]{message});
        requestBody.put("max_tokens", 500);
        requestBody.put("temperature", 0.3);
        
        return requestBody;
    }
    
    @SuppressWarnings("unchecked")
    private static AIReportData parseOpenAIResponse(String responseBody) {
        try {
            Map<String, Object> response = objectMapper.readValue(responseBody, Map.class);
            Map<String, Object> choice = ((Map<String, Object>) ((Object[]) response.get("choices"))[0]);
            Map<String, Object> message = (Map<String, Object>) choice.get("message");
            String content = (String) message.get("content");
            
            // Parse the JSON content from AI response
            Map<String, String> aiData = objectMapper.readValue(content, Map.class);
            
            return new AIReportData(
                aiData.get("stepDescription"),
                aiData.get("acceptanceCriteria"),
                aiData.get("actualResult"),
                aiData.get("screenshotName"),
                aiData.get("businessValue"),
                aiData.get("userStory")
            );
            
        } catch (Exception e) {
            System.err.println("Failed to parse AI response: " + e.getMessage());
            return null;
        }
    }
    
    private static AIReportData getFallbackReportData(String action, WebElement element) {
        String elementName = getElementName(element);
        return new AIReportData(
            action + " on " + elementName,
            action + " should be completed successfully",
            action + " is getting completed successfully",
            action.toLowerCase() + "_" + elementName.replaceAll("[^a-zA-Z0-9]", "_"),
            "Enables user interaction with the application",
            "As a user, I want to " + action.toLowerCase() + " so that I can proceed with my task"
        );
    }
    
    private static String getElementName(WebElement element) {
        try {
            String name = element.getAttribute("data-testid");
            if (name != null && !name.isEmpty()) return name;
            
            name = element.getAttribute("id");
            if (name != null && !name.isEmpty()) return name;
            
            name = element.getText();
            if (name != null && !name.isEmpty() && name.length() < 30) return name;
            
            return element.getTagName();
        } catch (Exception e) {
            return "element";
        }
    }
    
    /**
     * Generate content with business context analysis
     */
    public static AIReportData generateContextAwareContent(String action, WebElement element, 
                                                         String currentUrl, String previousAction) {
        String pageContext = analyzePageContext(currentUrl);
        String businessContext = inferBusinessContext(action, previousAction, pageContext);
        
        return generateReportContent(action, element, pageContext, businessContext);
    }
    
    private static String analyzePageContext(String url) {
        if (url.contains("login")) return "Authentication Page";
        if (url.contains("register") || url.contains("signup")) return "Registration Page";
        if (url.contains("dashboard")) return "Dashboard Page";
        if (url.contains("profile")) return "User Profile Page";
        if (url.contains("settings")) return "Settings Page";
        if (url.contains("checkout")) return "Checkout Page";
        if (url.contains("cart")) return "Shopping Cart Page";
        return "Application Page";
    }
    
    private static String inferBusinessContext(String action, String previousAction, String pageContext) {
        if (pageContext.contains("Authentication")) return "User Authentication Flow";
        if (pageContext.contains("Registration")) return "User Onboarding Flow";
        if (pageContext.contains("Checkout")) return "Purchase Flow";
        if (pageContext.contains("Cart")) return "Shopping Flow";
        if (action.toLowerCase().contains("submit")) return "Data Submission Flow";
        if (action.toLowerCase().contains("search")) return "Information Retrieval Flow";
        return "General User Interaction";
    }
    
    // Data class for AI-generated report content
    public static class AIReportData {
        public final String stepDescription;
        public final String acceptanceCriteria;
        public final String actualResult;
        public final String screenshotName;
        public final String businessValue;
        public final String userStory;
        
        public AIReportData(String stepDescription, String acceptanceCriteria, String actualResult,
                           String screenshotName, String businessValue, String userStory) {
            this.stepDescription = stepDescription;
            this.acceptanceCriteria = acceptanceCriteria;
            this.actualResult = actualResult;
            this.screenshotName = screenshotName;
            this.businessValue = businessValue;
            this.userStory = userStory;
        }
        
        public String getEnhancedStepDescription() {
            return stepDescription + " [" + businessValue + "]";
        }
        
        public String getFormattedUserStory() {
            return "User Story: " + userStory;
        }
    }
}
