package com.Automation.Scripts;

import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.openqa.selenium.support.ui.ExpectedConditions;
import java.time.Duration;

/**
 * Simple Training Venue Script using EPIQ Application Patterns
 * Based on actual patterns found in the codebase
 */
public class SimpleEPIQPatternScript {
    
    public static void main(String[] args) {
        WebDriver driver = new ChromeDriver();
        WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(10));
        
        try {
            // Navigate to application
            driver.get("http://cqmsconfig05/EPIQ/Pages/MainPage?TaskId=#");
            driver.manage().window().maximize();
            Thread.sleep(3000);
            
            System.out.println("=== EPIQ Training Venue Registration ===");
            
            // Fill fields using EPIQ patterns
            fillEPIQField(driver, wait, "VenueName", "Conference Hall A");
            fillEPIQField(driver, wait, "RoomNumber", "Room-101");
            fillEPIQField(driver, wait, "Capacity", "50");
            fillEPIQField(driver, wait, "PhoneNumber", "9876543210");
            fillEPIQField(driver, wait, "Address1", "123 Business Park");
            fillEPIQField(driver, wait, "City", "New York");
            fillEPIQField(driver, wait, "PinZip", "10001");
            fillEPIQField(driver, wait, "CostPerHour", "100");
            fillEPIQField(driver, wait, "UniqueCode", "VENUE001");
            fillEPIQField(driver, wait, "RoomType", "Conference Room");
            fillEPIQField(driver, wait, "ContactPerson", "John Doe");
            fillEPIQField(driver, wait, "EmailID", "<EMAIL>");
            fillEPIQField(driver, wait, "Address2", "Building A, Floor 2");
            fillEPIQField(driver, wait, "State", "NY");
            
            // Fill textarea fields
            fillEPIQTextArea(driver, wait, "RoomEquipment", "Projector, Whiteboard, Audio System");
            fillEPIQTextArea(driver, wait, "AdditionalInfo", "Air conditioned room with parking facility");
            
            // Submit form
            submitEPIQForm(driver, wait);
            
            System.out.println("=== Form submission completed ===");
            Thread.sleep(5000);
            
        } catch (Exception e) {
            System.out.println("Error: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // Uncomment to close browser
            // driver.quit();
        }
    }
    
    /**
     * Fill field using EPIQ application patterns
     */
    private static void fillEPIQField(WebDriver driver, WebDriverWait wait, String fieldName, String value) {
        String[] idPatterns = {
            "TrainingVenue_" + fieldName,    // Full prefix pattern
            fieldName,                       // Simple field name
            fieldName.toLowerCase(),         // Lowercase
            fieldName.toUpperCase()          // Uppercase
        };
        
        String[] namePatterns = {
            fieldName,
            fieldName.toLowerCase(),
            "TrainingVenue_" + fieldName
        };
        
        String[] xpathPatterns = {
            "//input[@id='" + fieldName + "']",
            "//input[@name='" + fieldName + "']",
            "//input[contains(@id,'" + fieldName + "')]",
            "//input[contains(@name,'" + fieldName + "')]",
            "//input[contains(@id,'" + fieldName.toLowerCase() + "')]",
            "//input[contains(@name,'" + fieldName.toLowerCase() + "')]"
        };
        
        // Try ID patterns
        for (String id : idPatterns) {
            if (fillByStrategy(driver, By.id(id), value, "ID: " + id)) {
                System.out.println("✓ Filled " + fieldName + " using ID: " + id);
                return;
            }
        }
        
        // Try name patterns
        for (String name : namePatterns) {
            if (fillByStrategy(driver, By.name(name), value, "Name: " + name)) {
                System.out.println("✓ Filled " + fieldName + " using Name: " + name);
                return;
            }
        }
        
        // Try XPath patterns
        for (String xpath : xpathPatterns) {
            if (fillByStrategy(driver, By.xpath(xpath), value, "XPath: " + xpath)) {
                System.out.println("✓ Filled " + fieldName + " using XPath");
                return;
            }
        }
        
        System.out.println("✗ Could not fill " + fieldName);
    }
    
    /**
     * Fill textarea using EPIQ patterns
     */
    private static void fillEPIQTextArea(WebDriver driver, WebDriverWait wait, String fieldName, String value) {
        String[] locators = {
            "//textarea[@id='" + fieldName + "']",
            "//textarea[@name='" + fieldName + "']",
            "//textarea[@id='TrainingVenue_" + fieldName + "']",
            "//textarea[contains(@id,'" + fieldName + "')]",
            "//textarea[contains(@name,'" + fieldName + "')]"
        };
        
        for (String locator : locators) {
            if (fillByStrategy(driver, By.xpath(locator), value, "Textarea: " + locator)) {
                System.out.println("✓ Filled " + fieldName + " textarea");
                return;
            }
        }
        
        System.out.println("✗ Could not fill " + fieldName + " textarea");
    }
    
    /**
     * Submit form using EPIQ patterns
     */
    private static void submitEPIQForm(WebDriver driver, WebDriverWait wait) {
        String[] submitLocators = {
            "btnSubmit",                           // Most common EPIQ pattern
            "Submit",
            "TrainingVenue_Submit",
            "//button[@id='btnSubmit']",
            "//input[@type='submit']",
            "//button[text()='Submit']",
            "//input[@value='Submit']",
            "//button[contains(@class,'submit')]",
            "//button[contains(text(),'Submit')]"
        };
        
        for (String locator : submitLocators) {
            try {
                WebElement submitBtn = null;
                
                // Try as ID first
                if (!locator.startsWith("//")) {
                    try {
                        submitBtn = driver.findElement(By.id(locator));
                    } catch (Exception e) {
                        continue;
                    }
                } else {
                    try {
                        submitBtn = driver.findElement(By.xpath(locator));
                    } catch (Exception e) {
                        continue;
                    }
                }
                
                if (submitBtn != null && submitBtn.isDisplayed() && submitBtn.isEnabled()) {
                    submitBtn.click();
                    System.out.println("✓ Submit clicked using: " + locator);
                    return;
                }
            } catch (Exception e) {
                continue;
            }
        }
        
        System.out.println("✗ Could not find submit button");
    }
    
    /**
     * Helper method to fill field by locator strategy
     */
    private static boolean fillByStrategy(WebDriver driver, By locator, String value, String strategy) {
        try {
            WebElement element = driver.findElement(locator);
            if (element.isDisplayed() && element.isEnabled()) {
                element.clear();
                element.sendKeys(value);
                Thread.sleep(200);
                return true;
            }
        } catch (Exception e) {
            // Element not found or not interactable
        }
        return false;
    }
}
