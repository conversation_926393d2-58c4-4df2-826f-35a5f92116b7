package com.Automation.Tests;

import org.testng.annotations.*;
import com.Automation.Utils.SimpleExtentReporter;
import com.Automation.learniqBase.SimpleReportingEngine;
import com.Automation.Utils.ConfigsReader;

/**
 * Sample test class demonstrating simple ExtentReports usage
 * Shows clean test results without detailed step tables
 */
public class SimpleReportingTest extends SimpleReportingEngine {
    
    @BeforeSuite
    public void setupSuite() {
        // Initialize simple reporting
        SimpleExtentReporter.initializeReport("LearnIQ Simple Test Suite", driver);
        System.out.println("Simple reporting initialized");
    }
    
    @BeforeMethod
    public void setupTest() {
        // Enable simple reporting mode
        enableSimpleReporting();
    }
    
    @Test(priority = 1)
    public void testTopicRegistration() {
        // Start test in report
        SimpleExtentReporter.startTest("Topic Registration Test", 
            "Verify user can register for a topic successfully");
        
        try {
            // Test steps with simple logging
            logInfo("Starting Topic Registration test");
            
            // Navigate to application
            simpleNavigate(ConfigsReader.getPropValue("url"));
            
            // Login steps (example)
            logAction("Performing login");
            // Your existing login logic here...
            
            // Topic registration steps
            logAction("Navigating to Topic Registration");
            // Your topic registration logic here...
            
            logAction("Filling registration form");
            // Your form filling logic here...
            
            logAction("Submitting registration");
            // Your submission logic here...
            
            // Mark test as passed
            markTestPassed();
            
        } catch (Exception e) {
            // Mark test as failed with reason
            markTestFailed("Topic registration failed: " + e.getMessage());
            throw e;
        }
    }
    
    @Test(priority = 2)
    public void testCourseRegistration() {
        SimpleExtentReporter.startTest("Course Registration Test", 
            "Verify user can register for a course successfully");
        
        try {
            logInfo("Starting Course Registration test");
            
            // Your course registration test logic here...
            logAction("Navigating to Course Registration");
            logAction("Selecting course");
            logAction("Completing registration");
            
            markTestPassed();
            
        } catch (Exception e) {
            markTestFailed("Course registration failed: " + e.getMessage());
            throw e;
        }
    }
    
    @Test(priority = 3)
    public void testTrainerModification() {
        SimpleExtentReporter.startTest("Trainer Modification Test", 
            "Verify trainer details can be modified");
        
        try {
            logInfo("Starting Trainer Modification test");
            
            // Your trainer modification test logic here...
            logAction("Accessing trainer profile");
            logAction("Modifying trainer details");
            logAction("Saving changes");
            
            markTestPassed();
            
        } catch (Exception e) {
            markTestFailed("Trainer modification failed: " + e.getMessage());
            throw e;
        }
    }
    
    @Test(priority = 4, enabled = false) // Disabled test example
    public void testDisabledFeature() {
        SimpleExtentReporter.startTest("Disabled Feature Test", 
            "This test is currently disabled");
        
        SimpleExtentReporter.markTestSkipped("Feature not yet implemented");
    }
    
    @AfterSuite
    public void tearDownSuite() {
        // Finalize and generate report
        SimpleExtentReporter.finalizeReport();
        System.out.println("Simple report generated successfully");
    }
}
