package com.Automation.Scripts;

import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.chrome.ChromeDriver;

/**
 * Simple Training Venue Script using contains() in XPath
 * Most flexible approach for partial matching
 */
public class SimpleContainsScript {
    
    public static void main(String[] args) {
        WebDriver driver = new ChromeDriver();
        
        try {
            // Navigate to application
            driver.get("http://cqmsconfig05/EPIQ/Pages/MainPage?TaskId=#");
            driver.manage().window().maximize();
            Thread.sleep(3000);
            
            System.out.println("=== Training Venue Registration - Contains() Approach ===");
            
            // Fill mandatory fields using contains()
            fillField(driver, "//input[contains(@name,'Venue') or contains(@id,'Venue')]", "Conference Hall A", "Venue Name");
            fillField(driver, "//input[contains(@name,'Room') or contains(@id,'Room')]", "Room-101", "Room Number");
            fillField(driver, "//input[contains(@name,'Capacity') or contains(@id,'Capacity')]", "50", "Capacity");
            fillField(driver, "//input[contains(@name,'Phone') or contains(@id,'Phone')]", "9876543210", "Phone Number");
            fillField(driver, "//input[contains(@name,'Unique') or contains(@id,'Unique') or contains(@name,'Code') or contains(@id,'Code')]", "VENUE001", "Unique Code");
            fillField(driver, "//input[contains(@name,'Type') or contains(@id,'Type')]", "Conference Room", "Room Type");
            fillField(driver, "//input[contains(@name,'Contact') or contains(@id,'Contact') or contains(@name,'Person') or contains(@id,'Person')]", "John Doe", "Contact Person");
            
            // Fill optional fields
            fillField(driver, "//input[contains(@name,'Address1') or contains(@id,'Address1')]", "123 Business Park", "Address1");
            fillField(driver, "//input[contains(@name,'City') or contains(@id,'City')]", "New York", "City");
            fillField(driver, "//input[contains(@name,'Pin') or contains(@id,'Pin') or contains(@name,'Zip') or contains(@id,'Zip')]", "10001", "Pin/Zip");
            fillField(driver, "//input[contains(@name,'Cost') or contains(@id,'Cost')]", "100", "Cost Per Hour");
            fillField(driver, "//input[contains(@name,'Email') or contains(@id,'Email')]", "<EMAIL>", "Email ID");
            fillField(driver, "//input[contains(@name,'Address2') or contains(@id,'Address2')]", "Building A, Floor 2", "Address2");
            fillField(driver, "//input[contains(@name,'State') or contains(@id,'State')]", "NY", "State");
            
            // Fill textarea fields
            fillField(driver, "//textarea[contains(@name,'Equipment') or contains(@id,'Equipment')]", "Projector, Whiteboard, Audio System", "Room Equipment");
            fillField(driver, "//textarea[contains(@name,'Additional') or contains(@id,'Additional') or contains(@name,'Info') or contains(@id,'Info')]", "Air conditioned room with parking facility", "Additional Info");
            
            // Submit form
            submitForm(driver);
            
            System.out.println("=== Form Submitted Successfully ===");
            Thread.sleep(5000);
            
        } catch (Exception e) {
            System.out.println("Error: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // driver.quit(); // Uncomment to close browser
        }
    }
    
    /**
     * Fill field using contains() XPath
     */
    private static void fillField(WebDriver driver, String xpath, String value, String fieldName) {
        try {
            WebElement field = driver.findElement(By.xpath(xpath));
            
            if (field.isDisplayed() && field.isEnabled()) {
                field.clear();
                field.sendKeys(value);
                System.out.println("✓ " + fieldName + ": " + value);
                Thread.sleep(200);
            }
            
        } catch (Exception e) {
            System.out.println("✗ Failed to fill " + fieldName + ": " + e.getMessage());
        }
    }
    
    /**
     * Submit form using contains() approach
     */
    private static void submitForm(WebDriver driver) {
        try {
            // Multiple submit button contains() locators
            String[] submitXPaths = {
                "//button[contains(text(),'Submit')]",
                "//input[@type='submit']",
                "//button[contains(@id,'Submit')]",
                "//input[contains(@value,'Submit')]",
                "//button[contains(@class,'submit')]",
                "//input[contains(@class,'submit')]"
            };
            
            for (String xpath : submitXPaths) {
                try {
                    WebElement submitBtn = driver.findElement(By.xpath(xpath));
                    if (submitBtn.isDisplayed() && submitBtn.isEnabled()) {
                        submitBtn.click();
                        System.out.println("✓ Submit button clicked using: " + xpath);
                        return;
                    }
                } catch (Exception e) {
                    continue;
                }
            }
            
            System.out.println("✗ Could not find submit button");
            
        } catch (Exception e) {
            System.out.println("✗ Submit failed: " + e.getMessage());
        }
    }
}

/**
 * Alternative contains() approaches
 */
class ContainsAlternatives {
    
    /**
     * Simple contains() approach - Copy & Paste Ready
     */
    public static void simpleContains(WebDriver driver) {
        try {
            // Simple contains() locators
            driver.findElement(By.xpath("//input[contains(@name,'Venue')]")).sendKeys("Conference Hall A");
            driver.findElement(By.xpath("//input[contains(@name,'Room')]")).sendKeys("Room-101");
            driver.findElement(By.xpath("//input[contains(@name,'Capacity')]")).sendKeys("50");
            driver.findElement(By.xpath("//input[contains(@name,'Phone')]")).sendKeys("9876543210");
            driver.findElement(By.xpath("//input[contains(@name,'Unique')]")).sendKeys("VENUE001");
            driver.findElement(By.xpath("//input[contains(@name,'Contact')]")).sendKeys("John Doe");
            
            // Submit
            driver.findElement(By.xpath("//button[contains(text(),'Submit')]")).click();
            
        } catch (Exception e) {
            System.out.println("Simple contains approach failed: " + e.getMessage());
        }
    }
    
    /**
     * Multiple contains() conditions
     */
    public static void multipleContains(WebDriver driver) {
        try {
            // Multiple contains() conditions for better matching
            driver.findElement(By.xpath("//input[contains(@name,'Venue') or contains(@id,'Venue') or contains(@class,'venue')]")).sendKeys("Conference Hall A");
            driver.findElement(By.xpath("//input[contains(@name,'Room') or contains(@id,'Room') or contains(@class,'room')]")).sendKeys("Room-101");
            driver.findElement(By.xpath("//input[contains(@name,'Capacity') or contains(@id,'Capacity') or contains(@class,'capacity')]")).sendKeys("50");
            
            // Submit
            driver.findElement(By.xpath("//button[contains(text(),'Submit') or contains(@id,'Submit') or contains(@class,'submit')]")).click();
            
        } catch (Exception e) {
            System.out.println("Multiple contains approach failed: " + e.getMessage());
        }
    }
    
    /**
     * Case-insensitive contains() approach
     */
    public static void caseInsensitiveContains(WebDriver driver) {
        try {
            // Using translate() for case-insensitive matching
            driver.findElement(By.xpath("//input[contains(translate(@name,'ABCDEFGHIJKLMNOPQRSTUVWXYZ','abcdefghijklmnopqrstuvwxyz'),'venue')]")).sendKeys("Conference Hall A");
            driver.findElement(By.xpath("//input[contains(translate(@name,'ABCDEFGHIJKLMNOPQRSTUVWXYZ','abcdefghijklmnopqrstuvwxyz'),'room')]")).sendKeys("Room-101");
            
            // Submit
            driver.findElement(By.xpath("//button[contains(translate(text(),'ABCDEFGHIJKLMNOPQRSTUVWXYZ','abcdefghijklmnopqrstuvwxyz'),'submit')]")).click();
            
        } catch (Exception e) {
            System.out.println("Case-insensitive contains approach failed: " + e.getMessage());
        }
    }
    
    /**
     * Contains with text content
     */
    public static void containsWithText(WebDriver driver) {
        try {
            // Using contains() with label text to find associated inputs
            driver.findElement(By.xpath("//label[contains(text(),'Venue')]/following-sibling::input | //label[contains(text(),'Venue')]/..//input")).sendKeys("Conference Hall A");
            driver.findElement(By.xpath("//label[contains(text(),'Room')]/following-sibling::input | //label[contains(text(),'Room')]/..//input")).sendKeys("Room-101");
            driver.findElement(By.xpath("//label[contains(text(),'Capacity')]/following-sibling::input | //label[contains(text(),'Capacity')]/..//input")).sendKeys("50");
            
            // Submit
            driver.findElement(By.xpath("//button[contains(text(),'Submit')]")).click();
            
        } catch (Exception e) {
            System.out.println("Contains with text approach failed: " + e.getMessage());
        }
    }
    
    /**
     * Ready-to-use contains() locators
     */
    public static void readyToUseContains(WebDriver driver) {
        try {
            System.out.println("=== Ready-to-use Contains() Locators ===");
            
            // Copy-paste ready locators
            driver.findElement(By.xpath("//input[contains(@name,'Venue') or contains(@id,'Venue')]")).sendKeys("Conference Hall A");
            driver.findElement(By.xpath("//input[contains(@name,'Room') or contains(@id,'Room')]")).sendKeys("Room-101");
            driver.findElement(By.xpath("//input[contains(@name,'Capacity') or contains(@id,'Capacity')]")).sendKeys("50");
            driver.findElement(By.xpath("//input[contains(@name,'Phone') or contains(@id,'Phone')]")).sendKeys("9876543210");
            driver.findElement(By.xpath("//input[contains(@name,'Unique') or contains(@id,'Unique')]")).sendKeys("VENUE001");
            driver.findElement(By.xpath("//input[contains(@name,'Type') or contains(@id,'Type')]")).sendKeys("Conference Room");
            driver.findElement(By.xpath("//input[contains(@name,'Contact') or contains(@id,'Contact')]")).sendKeys("John Doe");
            
            // Optional fields
            driver.findElement(By.xpath("//input[contains(@name,'Email') or contains(@id,'Email')]")).sendKeys("<EMAIL>");
            driver.findElement(By.xpath("//input[contains(@name,'City') or contains(@id,'City')]")).sendKeys("New York");
            driver.findElement(By.xpath("//input[contains(@name,'State') or contains(@id,'State')]")).sendKeys("NY");
            
            // Textarea fields
            driver.findElement(By.xpath("//textarea[contains(@name,'Equipment') or contains(@id,'Equipment')]")).sendKeys("Projector, Whiteboard");
            driver.findElement(By.xpath("//textarea[contains(@name,'Additional') or contains(@id,'Additional')]")).sendKeys("Air conditioned room");
            
            // Submit
            driver.findElement(By.xpath("//button[contains(text(),'Submit')] | //input[@type='submit']")).click();
            
            System.out.println("✓ All fields filled using contains() approach");
            
        } catch (Exception e) {
            System.out.println("Ready-to-use contains approach failed: " + e.getMessage());
        }
    }
}
