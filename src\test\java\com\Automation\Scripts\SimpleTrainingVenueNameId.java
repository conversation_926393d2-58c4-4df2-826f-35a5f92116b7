package com.Automation.Scripts;

import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.chrome.ChromeDriver;

/**
 * Simple Training Venue Script using Name/ID locators
 * Copy-paste ready code
 */
public class SimpleTrainingVenueNameId {
    
    public static void main(String[] args) {
        WebDriver driver = new ChromeDriver();
        
        try {
            // Navigate to application
            driver.get("http://cqmsconfig05/EPIQ/Pages/MainPage?TaskId=#");
            driver.manage().window().maximize();
            Thread.sleep(3000);
            
            System.out.println("=== Training Venue Registration ===");
            
            // Fill mandatory fields using name locators
            fillField(driver, "name", "VenueName", "Conference Hall A");
            fillField(driver, "name", "RoomNumber", "Room-101");
            fillField(driver, "name", "Capacity", "50");
            fillField(driver, "name", "PhoneNumber", "9876543210");
            fillField(driver, "name", "UniqueCode", "VENUE001");
            fillField(driver, "name", "RoomType", "Conference Room");
            fillField(driver, "name", "ContactPerson", "John Doe");
            
            // Fill optional fields
            fillField(driver, "name", "Address1", "123 Business Park");
            fillField(driver, "name", "City", "New York");
            fillField(driver, "name", "PinZip", "10001");
            fillField(driver, "name", "CostPerHour", "100");
            fillField(driver, "name", "EmailID", "<EMAIL>");
            fillField(driver, "name", "Address2", "Building A, Floor 2");
            fillField(driver, "name", "State", "NY");
            fillField(driver, "name", "RoomEquipment", "Projector, Whiteboard, Audio System");
            fillField(driver, "name", "AdditionalInfo", "Air conditioned room with parking facility");
            
            // Submit form
            submitForm(driver);
            
            System.out.println("=== Form Submitted Successfully ===");
            Thread.sleep(5000);
            
        } catch (Exception e) {
            System.out.println("Error: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // driver.quit(); // Uncomment to close browser
        }
    }
    
    /**
     * Fill field using name or id locator
     */
    private static void fillField(WebDriver driver, String locatorType, String locatorValue, String value) {
        try {
            WebElement field = null;
            
            if ("name".equals(locatorType)) {
                field = driver.findElement(By.name(locatorValue));
            } else if ("id".equals(locatorType)) {
                field = driver.findElement(By.id(locatorValue));
            }
            
            if (field != null && field.isDisplayed() && field.isEnabled()) {
                field.clear();
                field.sendKeys(value);
                System.out.println("✓ " + locatorValue + ": " + value);
                Thread.sleep(200);
            }
            
        } catch (Exception e) {
            // Try alternative locator if first fails
            try {
                WebElement field = null;
                
                if ("name".equals(locatorType)) {
                    // Try id if name fails
                    field = driver.findElement(By.id(locatorValue));
                } else {
                    // Try name if id fails
                    field = driver.findElement(By.name(locatorValue));
                }
                
                if (field != null && field.isDisplayed() && field.isEnabled()) {
                    field.clear();
                    field.sendKeys(value);
                    System.out.println("✓ " + locatorValue + " (alternative): " + value);
                    Thread.sleep(200);
                }
                
            } catch (Exception e2) {
                System.out.println("✗ Failed to fill " + locatorValue);
            }
        }
    }
    
    /**
     * Submit form using multiple strategies
     */
    private static void submitForm(WebDriver driver) {
        try {
            // Try multiple submit button locators
            String[] submitLocators = {
                "//button[text()='Submit']",
                "//input[@type='submit']",
                "//button[@id='btnSubmit']",
                "//input[@value='Submit']"
            };
            
            for (String locator : submitLocators) {
                try {
                    WebElement submitBtn = driver.findElement(By.xpath(locator));
                    if (submitBtn.isDisplayed() && submitBtn.isEnabled()) {
                        submitBtn.click();
                        System.out.println("✓ Submit button clicked");
                        return;
                    }
                } catch (Exception e) {
                    continue;
                }
            }
            
            System.out.println("✗ Could not find submit button");
            
        } catch (Exception e) {
            System.out.println("✗ Submit failed: " + e.getMessage());
        }
    }
}

/**
 * Alternative simple approaches
 */
class AlternativeApproaches {
    
    /**
     * Direct name locators approach
     */
    public static void directNameApproach(WebDriver driver) {
        try {
            driver.findElement(By.name("VenueName")).sendKeys("Conference Hall A");
            driver.findElement(By.name("RoomNumber")).sendKeys("Room-101");
            driver.findElement(By.name("Capacity")).sendKeys("50");
            driver.findElement(By.name("PhoneNumber")).sendKeys("9876543210");
            driver.findElement(By.name("UniqueCode")).sendKeys("VENUE001");
            driver.findElement(By.name("RoomType")).sendKeys("Conference Room");
            driver.findElement(By.name("ContactPerson")).sendKeys("John Doe");
            
            // Submit
            driver.findElement(By.xpath("//button[text()='Submit']")).click();
            
        } catch (Exception e) {
            System.out.println("Direct name approach failed: " + e.getMessage());
        }
    }
    
    /**
     * Direct id locators approach
     */
    public static void directIdApproach(WebDriver driver) {
        try {
            driver.findElement(By.id("VenueName")).sendKeys("Conference Hall A");
            driver.findElement(By.id("RoomNumber")).sendKeys("Room-101");
            driver.findElement(By.id("Capacity")).sendKeys("50");
            driver.findElement(By.id("PhoneNumber")).sendKeys("9876543210");
            driver.findElement(By.id("UniqueCode")).sendKeys("VENUE001");
            driver.findElement(By.id("RoomType")).sendKeys("Conference Room");
            driver.findElement(By.id("ContactPerson")).sendKeys("John Doe");
            
            // Submit
            driver.findElement(By.id("btnSubmit")).click();
            
        } catch (Exception e) {
            System.out.println("Direct id approach failed: " + e.getMessage());
        }
    }
    
    /**
     * Mixed locators approach
     */
    public static void mixedApproach(WebDriver driver) {
        try {
            // Try name first, fallback to id
            fillWithFallback(driver, "VenueName", "Conference Hall A");
            fillWithFallback(driver, "RoomNumber", "Room-101");
            fillWithFallback(driver, "Capacity", "50");
            fillWithFallback(driver, "PhoneNumber", "9876543210");
            fillWithFallback(driver, "UniqueCode", "VENUE001");
            fillWithFallback(driver, "RoomType", "Conference Room");
            fillWithFallback(driver, "ContactPerson", "John Doe");
            
            // Submit
            try {
                driver.findElement(By.xpath("//button[text()='Submit']")).click();
            } catch (Exception e) {
                driver.findElement(By.id("btnSubmit")).click();
            }
            
        } catch (Exception e) {
            System.out.println("Mixed approach failed: " + e.getMessage());
        }
    }
    
    private static void fillWithFallback(WebDriver driver, String fieldName, String value) {
        try {
            driver.findElement(By.name(fieldName)).sendKeys(value);
        } catch (Exception e) {
            try {
                driver.findElement(By.id(fieldName)).sendKeys(value);
            } catch (Exception e2) {
                System.out.println("Failed to fill: " + fieldName);
            }
        }
    }
}
