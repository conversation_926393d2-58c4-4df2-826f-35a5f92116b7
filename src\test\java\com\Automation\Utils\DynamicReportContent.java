package com.Automation.Utils;

import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.By;

/**
 * Dynamic Report Content Generator
 * Generates report content dynamically based on element properties and context
 */
public class DynamicReportContent {
    
    private static Properties reportProperties;
    private static Map<String, String> elementTypeMap;
    
    static {
        // Load report content properties
        reportProperties = new Properties();
        try {
            reportProperties.load(DynamicReportContent.class.getClassLoader()
                .getResourceAsStream("configs/configuration.properties"));
        } catch (Exception e) {
            System.err.println("Failed to load report properties: " + e.getMessage());
        }
        
        // Initialize element type mapping
        initializeElementTypeMap();
    }
    
    private static void initializeElementTypeMap() {
        elementTypeMap = new HashMap<>();
        elementTypeMap.put("button", "button");
        elementTypeMap.put("input", "field");
        elementTypeMap.put("select", "dropdown");
        elementTypeMap.put("textarea", "text area");
        elementTypeMap.put("a", "link");
        elementTypeMap.put("span", "element");
        elementTypeMap.put("div", "section");
    }
    
    /**
     * Generate dynamic step description for click actions
     */
    public static String generateClickStepDescription(WebElement element) {
        String elementName = getElementName(element);
        String elementType = getElementType(element);
        
        String template = reportProperties.getProperty("click.stepDescription", 
            "Click on '{elementName}' {elementType}");
        
        return template.replace("{elementName}", elementName)
                      .replace("{elementType}", elementType);
    }
    
    /**
     * Generate dynamic acceptance criteria for click actions
     */
    public static String generateClickAcceptanceCriteria(WebElement element) {
        String elementName = getElementName(element);
        
        String template = reportProperties.getProperty("click.acceptanceCriteria",
            "'{elementName}' should be clicked successfully and next screen should be displayed");
        
        return template.replace("{elementName}", elementName);
    }
    
    /**
     * Generate dynamic actual result for click actions
     */
    public static String generateClickActualResult(WebElement element) {
        String elementName = getElementName(element);
        
        String template = reportProperties.getProperty("click.actualResult",
            "'{elementName}' is getting clicked successfully and next screen is displayed");
        
        return template.replace("{elementName}", elementName);
    }
    
    /**
     * Generate dynamic step description for sendKeys actions
     */
    public static String generateSendKeysStepDescription(WebElement element, String inputValue) {
        String fieldName = getElementName(element);
        
        String template = reportProperties.getProperty("sendKeys.stepDescription",
            "Enter '{inputValue}' in '{fieldName}' field");
        
        return template.replace("{inputValue}", inputValue)
                      .replace("{fieldName}", fieldName);
    }
    
    /**
     * Generate dynamic acceptance criteria for sendKeys actions
     */
    public static String generateSendKeysAcceptanceCriteria(WebElement element) {
        String fieldName = getElementName(element);
        
        String template = reportProperties.getProperty("sendKeys.acceptanceCriteria",
            "Entered value should be displayed in '{fieldName}' field");
        
        return template.replace("{fieldName}", fieldName);
    }
    
    /**
     * Generate dynamic actual result for sendKeys actions
     */
    public static String generateSendKeysActualResult(WebElement element) {
        String fieldName = getElementName(element);
        
        String template = reportProperties.getProperty("sendKeys.actualResult",
            "Entered value is getting displayed in '{fieldName}' field");
        
        return template.replace("{fieldName}", fieldName);
    }
    
    /**
     * Extract element name from various attributes
     */
    private static String getElementName(WebElement element) {
        try {
            // Try different attributes to get meaningful name
            String name = element.getAttribute("data-testid");
            if (name != null && !name.isEmpty()) return name;
            
            name = element.getAttribute("id");
            if (name != null && !name.isEmpty()) return name;
            
            name = element.getAttribute("name");
            if (name != null && !name.isEmpty()) return name;
            
            name = element.getAttribute("placeholder");
            if (name != null && !name.isEmpty()) return name;
            
            name = element.getAttribute("title");
            if (name != null && !name.isEmpty()) return name;
            
            name = element.getText();
            if (name != null && !name.isEmpty()) return name;
            
            // Fallback to tag name
            return element.getTagName();
            
        } catch (Exception e) {
            return "Unknown Element";
        }
    }
    
    /**
     * Get element type based on tag name
     */
    private static String getElementType(WebElement element) {
        try {
            String tagName = element.getTagName().toLowerCase();
            return elementTypeMap.getOrDefault(tagName, "element");
        } catch (Exception e) {
            return "element";
        }
    }
    
    /**
     * Generate screenshot name dynamically
     */
    public static String generateScreenshotName(WebElement element, String action) {
        String elementName = getElementName(element);
        return action + "_" + elementName.replaceAll("[^a-zA-Z0-9]", "_");
    }
    
    /**
     * Generate complete report data for any action
     */
    public static ReportData generateReportData(String action, WebElement element, String... additionalParams) {
        switch (action.toLowerCase()) {
            case "click":
                return new ReportData(
                    generateClickStepDescription(element),
                    generateClickAcceptanceCriteria(element),
                    generateClickActualResult(element),
                    generateScreenshotName(element, "click")
                );
                
            case "sendkeys":
                String inputValue = additionalParams.length > 0 ? additionalParams[0] : "";
                return new ReportData(
                    generateSendKeysStepDescription(element, inputValue),
                    generateSendKeysAcceptanceCriteria(element),
                    generateSendKeysActualResult(element),
                    generateScreenshotName(element, "sendKeys")
                );
                
            default:
                return new ReportData(
                    "Perform " + action + " on element",
                    "Action should be performed successfully",
                    "Action is getting performed successfully",
                    generateScreenshotName(element, action)
                );
        }
    }
    
    /**
     * Data class to hold report information
     */
    public static class ReportData {
        public final String stepDescription;
        public final String acceptanceCriteria;
        public final String actualResult;
        public final String screenshotName;
        
        public ReportData(String stepDescription, String acceptanceCriteria, 
                         String actualResult, String screenshotName) {
            this.stepDescription = stepDescription;
            this.acceptanceCriteria = acceptanceCriteria;
            this.actualResult = actualResult;
            this.screenshotName = screenshotName;
        }
    }
}
