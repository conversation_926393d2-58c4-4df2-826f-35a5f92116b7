package com.Automation.Objects;

import java.util.HashMap;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import com.Automation.learniqBase.PageInitializer;
import com.Automation.learniqObjects.CM_TrainingVenueRegistration_XPath;
import com.Automation.learniqObjects.EPICloginpage;
import com.Automation.learniqObjects.Logout;
import com.Automation.Utils.ConfigsReader;

/**
 * Training Venue Registration Test Class - XPath Version
 * Uses name/id attributes within XPath expressions
 */
public class TrainingVenueRegistration_XPath_Test extends PageInitializer {

    private CM_TrainingVenueRegistration_XPath trainingVenueReg;
    private EPICloginpage epiclogin;

    @BeforeMethod
    public void setUp() {
        // Initialize page objects
        trainingVenueReg = new CM_TrainingVenueRegistration_XPath();
        epiclogin = new EPICloginpage();
    }

    @Test(priority = 1, description = "Training Venue Registration - XPath with Name/ID")
    public void testTrainingVenueRegistrationXPath() {
        System.out.println("=== Test: Training Venue Registration - XPath Locators ===");

        // Create test data
        HashMap<String, String> testData = new HashMap<>();
        testData.put("trainingVenueName", "Conference Hall A");
        testData.put("roomNumber", "Room-101");
        testData.put("capacity", "50");
        testData.put("phoneNumber", "9876543210");
        testData.put("uniqueCode", "VENUE001");
        testData.put("roomType", "Conference Room");
        testData.put("contactPerson", "John Doe");
        testData.put("address1", "123 Business Park");
        testData.put("city", "New York");
        testData.put("pinZip", "10001");
        testData.put("costPerHour", "100");
        testData.put("emailId", "<EMAIL>");
        testData.put("address2", "Building A, Floor 2");
        testData.put("state", "NY");
        testData.put("roomEquipment", "Projector, Whiteboard, Audio System");
        testData.put("additionalInfo", "Air conditioned room with parking facility");

        // Login to application
        epiclogin.loginToApplication(
                ConfigsReader.getPropValue("company"), 
                ConfigsReader.getPropValue("EpicUserID"),
                ConfigsReader.getPropValue("EpicUserPWD")
        );
        
        // Select plant
        epiclogin.plant1();

        // Navigate to Training Venue Registration and fill form
        trainingVenueReg.submitTrainingVenueForm(testData);

        // Logout
        Logout.signOutPage();
        
        System.out.println("=== XPath Test Completed ===");
    }

    @AfterMethod
    public void tearDown() {
        System.out.println("Test method completed");
    }
}
